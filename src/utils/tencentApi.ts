/**
 * 腾讯地图API工具类
 * 用于将门店信息同步到腾讯服务
 */

/**
 * 创建延迟
 */
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => {
    setTimeout(resolve, ms);
  });
};

/**
 * 生成唯一标识符
 * @returns 返回一个基于时间戳和随机数的唯一标识
 */
const generateUniqueId = (): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `${timestamp}_${random}`;
};

/**
 * 门店数据接口，对应Excel中的门店信息
 */
export interface StoreData {
  name: string;      // 子点名称
  lng: number;       // 子点x (经度)
  lat: number;       // 子点y (纬度)
  addr: string;      // 子点地址
  cpid: string;      // 子点cpid(门店id)
}

/**
 * 腾讯API请求参数接口
 */
interface TencentApiRequest {
  mapKind: string;
  reqKind: string;
  reqMethod: string;
  reqParam: string; // JSON字符串
}

/**
 * 单个门店数据结构
 */
interface TencentStoreItem {
  gas_data: string;
  name: string;
  lng: number;
  lat: number;
  longitude: number;
  latitude: number;
  addr: string;
  address: string;
  cpid: string;
  kfc_raw_id: string;
}

/**
 * reqParam对象的接口定义（单个门店数据）
 */
interface ReqParamData {
  dataid: string;
  source: string;
  type: number;
  status: number;
  data: string; // 改为字符串对象，包含单个门店数据
}

/**
 * 同步单个门店信息到腾讯服务
 * @param storeData 单个门店数据
 * @returns 返回同步是否成功
 */
export const syncSingleStoreToTencent = async (storeData: StoreData): Promise<boolean> => {
  try {
    // 转换为腾讯API所需的格式（单个对象）
    const tencentStoreItem: TencentStoreItem = {
      gas_data: 'my test data',
      name: storeData.name,
      lng: storeData.lng,
      lat: storeData.lat,
      longitude: storeData.lng,
      latitude: storeData.lat,
      addr: storeData.addr,
      address: storeData.addr,
      cpid: storeData.cpid,
      kfc_raw_id: storeData.cpid  // kfc_raw_id 使用 cpid 的值
    };

    // 构造reqParam对象（data现在是单个对象的字符串）
    const reqParamData: ReqParamData = {
      dataid: storeData.cpid,
      source: 'kfc_chesuqu_offline',
      type: 1,
      status: 1,
      data: JSON.stringify(tencentStoreItem) // 现在是单个对象
    };

    // 构造API请求参数
    const requestData: TencentApiRequest = {
      mapKind: '2',
      reqKind: '1',
      reqMethod: '/ws/native/lbsserver/api/lbsdata/create',
      reqParam: JSON.stringify(reqParamData)
    };

    // 构造FormData
    const formData = new FormData();
    formData.append('mapKind', requestData.mapKind);
    formData.append('reqKind', requestData.reqKind);
    formData.append('reqMethod', requestData.reqMethod);
    formData.append('reqParam', requestData.reqParam);

    // 发送请求到腾讯API（使用form-data格式）
    const response = await fetch('https://locate.yumchina.com/dt/kfc/restapi/lbsproxy', {
      method: 'POST',
      body: formData
    });

    // 检查响应状态
    if (response.ok) {
      const result = await response.json();
      console.log(`门店 ${storeData.name}(${storeData.cpid}) 同步成功:`, result);
      return true;
    } else {
      console.error(`门店 ${storeData.name}(${storeData.cpid}) 同步失败, HTTP状态:`, response.status);
      return false;
    }
  } catch (error) {
    console.error(`门店 ${storeData.name}(${storeData.cpid}) 同步出错:`, error);
    return false;
  }
};

/**
 * 逐个同步门店信息到腾讯服务
 * @param storesData 门店数据数组
 * @param onProgress 进度回调函数
 * @returns 返回同步成功的数量
 */
export const batchSyncStoresToTencent = async (
  storesData: StoreData[], 
  onProgress?: (completed: number, total: number, batchInfo: string) => void
): Promise<number> => {
  let successCount = 0;
  const total = storesData.length;

  console.log(`开始逐个同步 ${total} 个门店信息到腾讯服务...`);

  // 逐个处理每个门店
  for (let i = 0; i < storesData.length; i++) {
    const store = storesData[i];
    
    try {
      console.log(`正在同步第 ${i + 1}/${total} 个门店: ${store.name}(${store.cpid})`);
      
      const success = await syncSingleStoreToTencent(store);
      if (success) {
        successCount++;
      }
      
      // 调用进度回调
      if (onProgress) {
        const completed = i + 1;
        const batchInfo = `第${completed}/${total}个门店: ${store.name}`;
        onProgress(completed, total, batchInfo);
      }
      
      // 添加延迟，避免请求过于频繁
      if (i < storesData.length - 1) {
        await delay(200); // 每个请求间延迟200ms
      }
    } catch (error) {
      console.error(`同步第 ${i + 1} 个门店时发生错误:`, error);
    }
  }

  console.log(`腾讯同步完成: ${successCount}/${total} 个门店同步成功`);
  return successCount;
}; 