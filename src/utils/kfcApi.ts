import MD5 from 'crypto-js/md5';

/**
 * 创建延迟
 */
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => {
    setTimeout(resolve, ms);
  });
};

/**
 * 门店信息类型
 */
export interface StoreInfo {
  tel: string;          // 电话
  marketName: string;   // 省
  cityName: string;     // 市
  districtName: string; // 区
}

/**
 * 清理storeCode，去除_xx格式的后缀
 * 例如：YZN002_D1 -> YZN002
 */
const cleanStoreCode = (storeCode: string): string => {
  if (!storeCode) return storeCode;
  // 匹配下划线及其后面的所有字符
  const index = storeCode.indexOf('_');
  if (index > 0) {
    return storeCode.substring(0, index);
  }
  return storeCode;
};

/**
 * 调用KFC API获取门店信息
 * @param cleanedCodes 清理后的门店编号数组
 * @returns 清理后门店编号到门店信息的映射
 */
const fetchStoreInfoFromAPI = async (cleanedCodes: string[]): Promise<{
  found: Record<string, StoreInfo>; // 找到的门店信息
  notFound: string[]; // 未找到的门店编号
}> => {
  // KFC API所需的认证信息
  const client_key = "kbaptkJGtByWrQaP";
  const client_sec = "jkJrRIacBwcpK68d";
  const path = "/cashier/emap/getStoreByCode";
  const kbcts = Date.now();
  
  // 请求参数 - 使用清理后的storeCode
  const params = {
    brand: "CNKFC",
    sys_channel: "preorder",
    appChannel: "",
    storeCodes: cleanedCodes.join(','),
    channelId: "kfcp"
  };
  
  // 生成签名字符串
  const signStr = `${client_key}\t${client_sec}\t${kbcts}\t${path}\t\t${JSON.stringify(params)}`;
  
  // 计算MD5签名
  const kbsv = MD5(signStr).toString();

  const response = await fetch('https://appcashier.kfc.com.cn/cashier/emap/getStoreByCode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'kbcts': kbcts.toString(),
      'kbck': client_key,
      'kbsv': kbsv
    },
    body: JSON.stringify(params)
  });

  const data = await response.json();
  
  const foundStores: Record<string, StoreInfo> = {};
  const inputStoreCodesSet = new Set(cleanedCodes);
  
  if (data?.errCode === 0 && Array.isArray(data?.data?.data)) {
    // 处理API返回的数据
    data.data.data.forEach((store: any) => {
      if (store.storeCode) {
        // 创建门店信息对象
        foundStores[store.storeCode] = {
          tel: store.phone || '',
          marketName: store.marketName || '',
          cityName: store.cityName || '',
          districtName: store.districtName || ''
        };
        
        // 从输入集合中移除已找到的storeCode
        inputStoreCodesSet.delete(store.storeCode);
      }
    });
  } else {
    console.error(`API返回错误:`, data);
  }
  
  // 剩余在集合中的storeCode即为未找到的
  const notFoundStoreCodes = Array.from(inputStoreCodesSet);
  
  return {
    found: foundStores,
    notFound: notFoundStoreCodes
  };
};

/**
 * 批量从KFC API获取门店信息（电话和省市区），包含重试机制
 * @param storeCodes 门店编号数组
 * @param enableRetry 是否启用重试机制，默认为false
 * @param onProgress 进度回调函数
 * @returns 门店编号到门店信息的映射
 */
export const batchFetchStoreInfoFromKFC = async (
  storeCodes: string[], 
  enableRetry: boolean = false, 
  onProgress?: (completed: number, total: number, batchInfo: string) => void
): Promise<Record<string, StoreInfo>> => {
  try {
    if (!storeCodes || storeCodes.length === 0) {
      return {};
    }

    // 结果映射（原始storeCode => 门店信息）
    const storeInfoMap: Record<string, StoreInfo> = {};
    
    // 1. 清理每个storeCode，去掉_后缀
    const cleanedCodeMap: Record<string, string> = {};  // 原始storeCode => 清理后storeCode
    const originalCodesMap: Record<string, string[]> = {};  // 清理后storeCode => 原始storeCode数组
    
    storeCodes.forEach(code => {
      const cleanedCode = cleanStoreCode(code);
      cleanedCodeMap[code] = cleanedCode;
      
      if (!originalCodesMap[cleanedCode]) {
        originalCodesMap[cleanedCode] = [];
      }
      originalCodesMap[cleanedCode].push(code);
    });
    
    // 2. 获取去重后的清理后storeCode列表
    const uniqueCleanedCodes = [...new Set(Object.values(cleanedCodeMap))];
    
    // 3. 将uniqueCleanedCodes分批，每批最多30个
    const batchSize = 30;
    const batches: string[][] = [];
    
    for (let i = 0; i < uniqueCleanedCodes.length; i += batchSize) {
      batches.push(uniqueCleanedCodes.slice(i, i + batchSize));
    }
    
    // 4. 记录所有未找到的门店ID
    let allNotFoundCodes: string[] = [];
    
    // 5. 处理每一批清理后的storeCode
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batchCodes = batches[batchIndex];
      const { found, notFound } = await fetchStoreInfoFromAPI(batchCodes);
      
      // 记录此批次未找到的门店ID
      allNotFoundCodes = [...allNotFoundCodes, ...notFound];
      
      // 处理找到的门店信息
      Object.entries(found).forEach(([cleanedCode, storeInfo]) => {
        // 找到对应此清理后storeCode的所有原始storeCode
        const correspondingOriginalCodes = originalCodesMap[cleanedCode] || [];
        
        // 将门店信息存入结果映射中
        correspondingOriginalCodes.forEach(originalCode => {
          storeInfoMap[originalCode] = storeInfo;
        });
      });
      
      // 调用进度回调
      if (onProgress) {
        const completed = Math.min((batchIndex + 1) * batchSize, storeCodes.length);
        const batchInfo = `第${batchIndex + 1}/${batches.length}批 (${batchCodes.length}个门店)`;
        onProgress(completed, storeCodes.length, batchInfo);
      }
      
      // 添加适当的延迟，避免频繁请求
      if (batches.length > 1 && batchIndex < batches.length - 1) {
        await delay(300);
      }
    }
    
    // 6. 只有在启用重试的情况下，对未找到的门店ID进行最多3次重试
    if (enableRetry && allNotFoundCodes.length > 0) {
      let retryCount = 0;
      let currentNotFoundCodes = [...allNotFoundCodes];
      
      while (currentNotFoundCodes.length > 0 && retryCount < 3) {
        retryCount++;
        console.log(`开始第${retryCount}次重试，尝试查询${currentNotFoundCodes.length}个未找到的门店信息...`);
        
        // 延迟一段时间后重试
        await delay(500);
        
        // 将未找到的门店ID分批
        const retryBatches: string[][] = [];
        for (let i = 0; i < currentNotFoundCodes.length; i += batchSize) {
          retryBatches.push(currentNotFoundCodes.slice(i, i + batchSize));
        }
        
        // 存储本轮重试后仍未找到的门店ID
        let stillNotFoundCodes: string[] = [];
        
        // 处理每一批重试的门店ID
        for (const retryBatch of retryBatches) {
          const { found, notFound } = await fetchStoreInfoFromAPI(retryBatch);
          
          // 更新仍未找到的门店ID列表
          stillNotFoundCodes = [...stillNotFoundCodes, ...notFound];
          
          // 处理找到的门店信息
          Object.entries(found).forEach(([cleanedCode, storeInfo]) => {
            // 找到对应此清理后storeCode的所有原始storeCode
            const correspondingOriginalCodes = originalCodesMap[cleanedCode] || [];
            
            // 将门店信息存入结果映射中
            correspondingOriginalCodes.forEach(originalCode => {
              storeInfoMap[originalCode] = storeInfo;
            });
          });
          
          // 添加延迟
          if (retryBatches.length > 1 && retryBatch !== retryBatches[retryBatches.length - 1]) {
            await delay(300);
          }
        }
        
        // 更新当前未找到的门店ID列表，用于下一轮重试
        currentNotFoundCodes = stillNotFoundCodes;
        
        console.log(`第${retryCount}次重试后，还有${currentNotFoundCodes.length}个门店未找到信息`);
      }
      
      // 如果重试3次后仍有未找到的门店ID，记录日志
      if (currentNotFoundCodes.length > 0) {
        console.warn(`在3次尝试后，以下${currentNotFoundCodes.length}个门店ID仍未找到信息:`, currentNotFoundCodes);
      }
    } else if (allNotFoundCodes.length > 0) {
      // 未启用重试但有未找到的门店ID，记录日志
      console.log(`有${allNotFoundCodes.length}个门店ID未找到信息，未启用重试机制`);
    }
    
    return storeInfoMap;
  } catch (error) {
    console.error('批量获取门店信息失败:', error);
    return {};
  }
};

/**
 * 批量从KFC API获取电话信息（向后兼容）
 * @param storeCodes 门店编号数组
 * @returns 门店编号到电话号码的映射
 */
export const batchFetchTelFromKFC = async (storeCodes: string[]): Promise<Record<string, string>> => {
  const storeInfoMap = await batchFetchStoreInfoFromKFC(storeCodes);
  
  // 转换为原来的格式（只返回电话信息）
  const telMap: Record<string, string> = {};
  Object.entries(storeInfoMap).forEach(([storeCode, info]) => {
    if (info.tel) {
      telMap[storeCode] = info.tel;
    }
  });
  
  return telMap;
};

/**
 * 获取单个门店的电话信息
 * @param storeCode 门店编号
 * @returns 电话号码或null
 */
export const fetchTelFromKFC = async (storeCode: string): Promise<string | null> => {
  if (!storeCode) {
    return null;
  }
  
  const telMap = await batchFetchTelFromKFC([storeCode]);
  return telMap[storeCode] || null;
}; 