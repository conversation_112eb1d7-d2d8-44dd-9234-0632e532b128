import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Image, Space, Tag, message, Switch, Typography, Button, Tooltip } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import React, { useRef, useState } from 'react';
import type { FeedbackRecord, FeedbackQueryParams } from '@/services/ant-design-pro/feedback';
import { queryFeedback } from '@/services/ant-design-pro/feedback';
import * as XLSX from 'xlsx';

/**
 * AI点餐反馈统计页面
 */
const AiFeedbackList: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  
  // 环境切换状态，默认为生产环境
  const [isProduction, setIsProduction] = useState(true);
  
  // 选中行状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<FeedbackRecord[]>([]);
  
  // 存储当前查询参数，用于导出功能
  const [currentQueryParams, setCurrentQueryParams] = useState<FeedbackQueryParams>({
    pageNo: 1,
    pageSize: 20,
  });

  // 解析图片URL列表
  const parseImageUrls = (imgUrl: string): string[] => {
    try {
      if (!imgUrl || imgUrl === '[]') return [];
      return JSON.parse(imgUrl);
    } catch (error) {
      return [];
    }
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  /**
   * 处理导出Excel功能
   * @param records 要导出的记录，不传则根据当前查询条件导出所有数据
   */
  const handleExport = async (records?: FeedbackRecord[]) => {
    const hide = message.loading('正在导出数据...');
    try {
      let dataToExport: FeedbackRecord[] = [];
      
      if (records && records.length > 0) {
        // 导出选中的记录
        dataToExport = records;
      } else {
        // 导出当前查询条件下的所有数据
        console.log('开始分页导出，查询参数:', currentQueryParams);
        
        const allData: FeedbackRecord[] = [];
        let currentPage = 1;
        const pageSize = 20; // 每页20条数据
        let hasMore = true;
        
        while (hasMore) {
          const queryParams: FeedbackQueryParams = {
            ...currentQueryParams,
            pageNo: currentPage,
            pageSize: pageSize,
          };
          
          console.log(`正在获取第${currentPage}页数据...`);
          const response = await queryFeedback(queryParams, isProduction);
          
          if (response && response.errCode === 0 && response.data.list) {
            allData.push(...response.data.list);
            
            // 检查是否还有更多数据
            hasMore = response.data.hasMore || (response.data.list.length === pageSize);
            currentPage++;
            
            console.log(`已获取${allData.length}条数据，是否还有更多: ${hasMore}`);
          } else {
            hasMore = false;
            if (response?.errCode !== 0) {
              throw new Error('获取数据失败');
            }
          }
        }
        
        dataToExport = allData;
        console.log(`数据获取完成，总共${dataToExport.length}条记录`);
      }
      
      if (dataToExport.length === 0) {
        hide();
        message.warning('没有数据可导出');
        return;
      }
      
      // 字段映射关系 - 与页面显示的列名保持一致
      const fieldMapping: Record<string, string> = {
        'lastupdatetime': '反馈时间',
        'id': 'ID',
        'phone': '手机号',
        'sessionId': 'Session ID',
        'type': '问题类别',
        'content': '反馈内容',
        'imgUrl': '图片列表',
        'videoUrl': '视频',
      };
      
      // 转换数据格式
      const excelData = dataToExport.map(item => {
        const rowData: Record<string, any> = {};
        
        Object.entries(fieldMapping).forEach(([dbField, excelField]) => {
          if (dbField === 'lastupdatetime') {
            // 格式化时间戳
            rowData[excelField] = formatTimestamp(item[dbField as keyof FeedbackRecord] as number);
          } else if (dbField === 'imgUrl') {
            // 处理图片URL列表
            const imageUrls = parseImageUrls(item[dbField] as string);
            rowData[excelField] = imageUrls.length > 0 ? imageUrls.join(', ') : '无图片';
          } else if (dbField === 'videoUrl') {
            // 处理视频URL
            rowData[excelField] = item[dbField] || '无视频';
          } else {
            rowData[excelField] = item[dbField as keyof FeedbackRecord] !== undefined 
              ? item[dbField as keyof FeedbackRecord] 
              : '';
          }
        });
        
        return rowData;
      });
      
      // 使用xlsx库将数据转换为Excel格式
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // 定义列宽
      const colWidths = [
        { wch: 20 }, // 反馈时间
        { wch: 10 }, // ID
        { wch: 15 }, // 手机号
        { wch: 25 }, // Session ID
        { wch: 15 }, // 问题类别
        { wch: 50 }, // 反馈内容
        { wch: 30 }, // 图片列表
        { wch: 20 }, // 视频
      ];
      worksheet['!cols'] = colWidths;
      
      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "AI点餐反馈数据");
      
      // 生成带时间戳的文件名
      const now = new Date();
      const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;
      const filename = `ai_feedback_${timestamp}.xlsx`;
      
      // 生成Excel文件并下载
      XLSX.writeFile(workbook, filename);
      
      hide();
      message.success(`导出成功！共导出 ${dataToExport.length} 条记录`);
    } catch (error) {
      hide();
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };

  /**
   * 导出选中的反馈记录
   */
  const handleExportSelected = () => {
    if (selectedRows.length === 0) {
      message.warning('请先选择要导出的反馈记录');
      return;
    }
    
    handleExport(selectedRows);
  };



  // 表格选中行的提醒渲染
  const TableAlertRender = () => {
    if (selectedRowKeys.length === 0) return null;
    
    return (
      <Space>
        <span>已选择 {selectedRowKeys.length} 项</span>
        <Button 
          type="link" 
          size="small"
          danger
          icon={<ExportOutlined />}
          onClick={handleExportSelected}
        >
          导出选中
        </Button>
        <Button 
          type="link" 
          size="small" 
          onClick={() => {
            setSelectedRowKeys([]);
            setSelectedRows([]);
          }}
        >
          取消选择
        </Button>
      </Space>
    );
  };

  // 表格列定义
  const columns: ProColumns<FeedbackRecord>[] = [
    {
      title: '反馈时间',
      dataIndex: 'lastupdatetime',
      width: 180,
      valueType: 'dateTimeRange',
      render: (_, record) => formatTimestamp(record.lastupdatetime),
      search: {
        transform: (value: any) => {
          if (Array.isArray(value) && value.length === 2) {
            return {
              startTime: value[0],
              endTime: value[1],
            };
          }
          return {};
        },
      },
      // 默认按反馈时间降序排序
      defaultSortOrder: 'descend',
      sorter: (a, b) => a.lastupdatetime - b.lastupdatetime,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
      copyable: true,
    },
    {
      title: 'Session ID',
      dataIndex: 'sessionId',
      width: 150,
      search: false,
      copyable: true,
    },
    {
      title: '问题类别',
      dataIndex: 'type',
      width: 150,
      search: false,
      render: (_, record) => (
        <Tag color="blue">{record.type}</Tag>
      ),
    },
    {
      title: '反馈内容',
      dataIndex: 'content',
      width: 300,
      search: false,
      ellipsis: {
        showTitle: false,
      },
      copyable: true,
      render: (_, record) => (
        <Tooltip title={record.content} placement="topLeft">
          <div 
            style={{ 
              maxWidth: 280,
              cursor: 'pointer',
              wordBreak: 'break-all',
              lineHeight: '1.4',
            }}
          >
            {record.content}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '图片列表',
      dataIndex: 'imgUrl',
      width: 200,
      search: false,
      render: (_, record) => {
        const imageUrls = parseImageUrls(record.imgUrl);
        if (imageUrls.length === 0) {
          return <span style={{ color: '#999' }}>无图片</span>;
        }
        return (
          <Space>
            {imageUrls.map((url, index) => (
              <Image
                key={index}
                width={40}
                height={40}
                src={url}
                preview={{
                  src: url,
                }}
                style={{
                  objectFit: 'cover',
                  borderRadius: 4,
                }}
              />
            ))}
          </Space>
        );
      },
    },
    {
      title: '视频',
      dataIndex: 'videoUrl',
      width: 120,
      search: false,
      render: (_, record) => {
        if (!record.videoUrl) {
          return <span style={{ color: '#999' }}>无视频</span>;
        }
        return (
          <a
            href={record.videoUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: '#1890ff' }}
          >
            查看视频
          </a>
        );
      },
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <ProTable<FeedbackRecord, FeedbackQueryParams>
        headerTitle={intl.formatMessage({
          id: 'pages.aiFeedback.title',
          defaultMessage: 'AI点餐反馈统计',
        })}
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
          optionRender: (searchConfig, formProps) => [
            <Button
              key="search"
              type="primary"
              onClick={() => {
                formProps?.form?.submit();
              }}
            >
              查询
            </Button>,
            <Button
              key="reset"
              danger
              onClick={() => {
                formProps?.form?.resetFields();
                formProps?.form?.submit();
              }}
            >
              重置
            </Button>,
          ],
        }}
        toolBarRender={() => [
          <Button
            key="export"
            type="primary"
            icon={<ExportOutlined />}
            onClick={() => handleExport()}
            disabled={selectedRowKeys.length > 0}
          >
            导出
          </Button>,
          <Space key="toolbar" align="center">
            <Typography.Text>环境切换：</Typography.Text>
            <Typography.Text type={isProduction ? 'success' : 'warning'}>
              {isProduction ? '生产环境' : '测试环境'}
            </Typography.Text>
            <Switch
              checked={isProduction}
              onChange={(checked) => {
                setIsProduction(checked);
                // 切换环境后自动刷新数据
                actionRef.current?.reload();
              }}
              checkedChildren="生产"
              unCheckedChildren="测试"
            />
          </Space>
        ]}
        tableAlertRender={() => <TableAlertRender />}
        request={async (params) => {
          try {
            console.log('Request params:', params);
            
            // 转换参数格式
            const queryParams: FeedbackQueryParams = {
              pageNo: params.current || 1,
              pageSize: params.pageSize || 20,
            };

            // 添加手机号搜索
            if (params.phone) {
              queryParams.phone = String(params.phone);
            }

            // 添加时间范围搜索
            if (params.startTime) {
              queryParams.startTime = String(params.startTime);
            }
            if (params.endTime) {
              queryParams.endTime = String(params.endTime);
            }

            // 保存当前查询参数，用于导出功能
            setCurrentQueryParams(queryParams);

            console.log('Query params:', queryParams);

            const response = await queryFeedback(queryParams, isProduction);
            
            console.log('API Response:', response);

            if (response && response.errCode === 0) {
              return {
                data: response.data.list,
                success: true,
                total: response.data.total,
              };
            } else {
              message.error('获取反馈数据失败');
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          } catch (error) {
            console.error('Error fetching feedback data:', error);
            message.error('获取反馈数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows as FeedbackRecord[]);
          },
        }}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        options={{
          reload: true,
          setting: {
            listsHeight: 400,
          },
        }}
        scroll={{ x: 1350 }}
      />
    </div>
  );
};

export default AiFeedbackList; 