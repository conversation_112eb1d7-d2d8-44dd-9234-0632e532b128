import React from 'react';
import { Modal, Progress, Typography, Button } from 'antd';
import { CloudUploadOutlined, CheckCircleOutlined, LoadingOutlined, WarningOutlined } from '@ant-design/icons';

const { Text, Title } = Typography;

export interface UploadProgressProps {
  visible: boolean;
  onCancel: () => void;
  // 数据录入进度
  dataImportProgress: {
    current: number;
    total: number;
    status: 'normal' | 'active' | 'success' | 'exception';
    text?: string;
  };
  // 腾讯同步进度 - 保留接口兼容性，但不使用
  tencentSyncProgress: {
    current: number;
    total: number;
    status: 'normal' | 'active' | 'success' | 'exception';
    text?: string;
  };
  // 整体状态
  overallStatus: 'uploading' | 'importing' | 'completed' | 'error';
}

const UploadProgress: React.FC<UploadProgressProps> = ({
  visible,
  onCancel,
  dataImportProgress,
  overallStatus,
}) => {
  const getProgressPercent = (current: number, total: number) => {
    if (total === 0) return 0;
    return Math.round((current / total) * 100);
  };

  const getStatusIcon = (status: string) => {
    if (status === 'success') {
      return <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />;
    }
    if (status === 'exception') {
      return <WarningOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />;
    }
    if (status === 'active') {
      return <LoadingOutlined style={{ color: '#1890ff', fontSize: '16px' }} />;
    }
    return null;
  };

  const getOverallStatusText = () => {
    switch (overallStatus) {
      case 'uploading':
        return '正在读取Excel文件...';
      case 'importing':
        return '正在处理数据录入...';
      case 'completed':
        return '数据导入完成！';
      case 'error':
        return '导入出现错误';
      default:
        return '';
    }
  };

  return (
    <Modal
      title={
        <div style={{ textAlign: 'center' }}>
          <CloudUploadOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          <span>Excel文件导入进度</span>
        </div>
      }
      open={visible}
      onCancel={overallStatus === 'completed' || overallStatus === 'error' ? onCancel : undefined}
      footer={[
        <Button 
          key="close"
          type="primary"
          onClick={onCancel}
          disabled={!['completed', 'error'].includes(overallStatus)}
        >
          {overallStatus === 'completed' ? '完成' : overallStatus === 'error' ? '关闭' : '处理中...'}
        </Button>
      ]}
      width={500}
      centered
      maskClosable={false}
      closable={overallStatus === 'completed' || overallStatus === 'error'}
    >
      <div style={{ padding: '20px 0' }}>
        {/* 整体状态指示 */}
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <Title level={4} style={{ margin: 0, color: overallStatus === 'error' ? '#ff4d4f' : '#1890ff' }}>
            {getOverallStatusText()}
          </Title>
        </div>

        {/* 数据录入进度 */}
        <div style={{
          padding: '20px',
          borderRadius: '8px',
          backgroundColor: overallStatus === 'importing' ? '#f0f8ff' : '#fafafa',
          border: `1px solid ${
            dataImportProgress.status === 'success' ? '#52c41a' :
            dataImportProgress.status === 'exception' ? '#ff4d4f' :
            overallStatus === 'importing' ? '#1890ff' : '#d9d9d9'
          }`,
          transition: 'all 0.3s ease',
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            {getStatusIcon(dataImportProgress.status)}
            <Text strong style={{ marginLeft: '8px', fontSize: '16px' }}>
              数据导入处理
            </Text>
          </div>
          
          <Progress
            percent={getProgressPercent(dataImportProgress.current, dataImportProgress.total)}
            status={
              dataImportProgress.status === 'exception' ? 'exception' :
              dataImportProgress.status === 'success' ? 'success' : 'active'
            }
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            strokeWidth={12}
            showInfo={true}
          />
          
          <div style={{ marginTop: '12px', display: 'flex', justifyContent: 'space-between' }}>
            <Text type="secondary">
              {dataImportProgress.text || '包含KFC API门店信息获取和数据处理'}
            </Text>
            <Text type="secondary" strong>
              {dataImportProgress.current} / {dataImportProgress.total}
            </Text>
          </div>
        </div>

        {/* 完成状态提示 */}
        {overallStatus === 'completed' && (
          <div style={{ 
            textAlign: 'center', 
            marginTop: '24px', 
            padding: '20px', 
            backgroundColor: '#f6ffed', 
            borderRadius: '8px',
            border: '1px solid #b7eb8f'
          }}>
            <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '32px', marginBottom: '12px' }} />
            <div>
              <Text strong style={{ color: '#52c41a', fontSize: '18px', display: 'block' }}>
                数据导入成功完成！
              </Text>
            </div>
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                成功导入 {dataImportProgress.current} 条记录
              </Text>
            </div>
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                如需同步到腾讯服务，请使用&quot;腾讯地址管理&quot;页面
              </Text>
            </div>
          </div>
        )}

        {/* 错误状态提示 */}
        {overallStatus === 'error' && (
          <div style={{ 
            textAlign: 'center', 
            marginTop: '24px', 
            padding: '20px', 
            backgroundColor: '#fff2f0', 
            borderRadius: '8px',
            border: '1px solid #ffccc7'
          }}>
            <WarningOutlined style={{ color: '#ff4d4f', fontSize: '32px', marginBottom: '12px' }} />
            <div>
              <Text strong style={{ color: '#ff4d4f', fontSize: '18px', display: 'block' }}>
                导入过程中出现错误
              </Text>
            </div>
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">
                请检查Excel文件格式或网络连接后重试
              </Text>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default UploadProgress; 