import { AddressItem } from '@/services/ant-design-pro/address';

export interface CreateFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values?: AddressItem) => Promise<void>;
}

const CreateForm: React.FC<CreateFormProps>;
export default CreateForm;

// 改为直接导出接口，不用模块声明
export interface EditorProps {
  visible: boolean;
  values: AddressItem;
  onCancel: () => void;
  onSubmit: (values?: AddressItem) => Promise<void>;
}
const Editor: React.FC<EditorProps>;
export default Editor;