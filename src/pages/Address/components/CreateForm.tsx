import React from 'react';
import { ModalForm, ProFormText, ProFormDigit } from '@ant-design/pro-components';
import { Form } from 'antd';
import type { AddressRecord } from '../../../../supabase/address';

interface CreateFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: AddressRecord) => Promise<void>;
}

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { visible, onCancel, onSubmit } = props;
  const [form] = Form.useForm();

  return (
    <ModalForm
      title="新建地址"
      form={form}
      open={visible}
      onFinish={async (values) => {
        await onSubmit(values as AddressRecord);
        form.resetFields();
        return true;
      }}
      modalProps={{
        onCancel: () => {
          onCancel();
          form.resetFields();
        },
        destroyOnClose: true,
      }}
    >
      <ProFormText
        name="name"
        label="名称"
        placeholder="请输入名称"
        rules={[{ required: true, message: '请输入名称' }]}
      />
      <ProFormText
        name="storeCodeId"
        label="门店ID"
        placeholder="请输入门店ID"
        rules={[{ required: true, message: '请输入门店ID' }]}
      />
      <ProFormText
        name="address"
        label="地址"
        placeholder="请输入地址"
        rules={[{ required: true, message: '请输入地址' }]}
      />
      <ProFormText
        name="cityName"
        label="城市"
        placeholder="请输入城市"
        rules={[{ required: true, message: '请输入城市' }]}
      />
      <ProFormText
        name="tel"
        label="电话"
        placeholder="请输入电话"
      />
      <ProFormDigit
        name="longitude"
        label="经度"
        placeholder="请输入经度"
        rules={[{ required: true, message: '请输入经度' }]}
        min={-180}
        max={180}
      />
      <ProFormDigit
        name="latitude"
        label="纬度"
        placeholder="请输入纬度"
        rules={[{ required: true, message: '请输入纬度' }]}
        min={-90}
        max={90}
      />
      <ProFormText
        name="storeCodePoiid"
        label="门店POI ID"
        placeholder="请输入门店POI ID"
      />
      <ProFormText
        name="marketName"
        label="市场"
        placeholder="请输入市场"
      />
      <ProFormText
        name="parentName"
        label="主点名称"
        placeholder="请输入主点名称"
      />
      <ProFormText
        name="parentStoreCodeId"
        label="母店号"
        placeholder="请输入母店号"
      />
      <ProFormText
        name="parentStoreCodePoiid"
        label="主点POI ID"
        placeholder="请输入主点POI ID"
      />
    </ModalForm>
  );
};

export default CreateForm; 