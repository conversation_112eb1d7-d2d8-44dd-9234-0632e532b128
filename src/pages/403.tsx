import { history } from '@umijs/max';
import { Button, Typography, Space } from 'antd';
import { SecurityScanOutlined, HomeOutlined } from '@ant-design/icons';
import React from 'react';
import { createStyles } from 'antd-style';

const { Title, Text } = Typography;

const useStyles = createStyles(({ token }) => ({
  container: {
    padding: '48px 24px',
    minHeight: '60vh',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    background: `linear-gradient(135deg, ${token.colorBgContainer} 0%, ${token.colorFillQuaternary} 100%)`,
  },
  errorTitle: {
    fontSize: '28px !important',
    fontWeight: 'bold',
    color: token.colorError,
    marginBottom: '16px !important',
    textShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  warningText: {
    fontSize: '18px',
    color: token.colorWarning,
    fontWeight: '600',
    padding: '12px 24px',
    background: `linear-gradient(90deg, ${token.colorWarningBg} 0%, ${token.colorWarningBgHover} 100%)`,
    borderRadius: '8px',
    border: `2px solid ${token.colorWarning}`,
    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
    marginBottom: '24px',
    display: 'inline-block',
  },
  descriptionText: {
    fontSize: '16px',
    color: token.colorTextSecondary,
    textAlign: 'center',
    lineHeight: '1.6',
    marginBottom: '32px',
  },
  actionButton: {
    height: '44px',
    fontSize: '16px',
    fontWeight: '500',
    boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)',
    borderRadius: '8px',
  },
  iconWrapper: {
    fontSize: '48px',
    color: token.colorWarning,
    marginBottom: '24px',
    filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))',
  },
}));

const NoAccessPage: React.FC = () => {
  const { styles } = useStyles();
  
  return (
    <div className={styles.container}>
      <Space direction="vertical" align="center" size="large">
        <div className={styles.iconWrapper}>
          <SecurityScanOutlined />
        </div>
        
        <Title level={1} className={styles.errorTitle}>
          403 - 访问被拒绝
        </Title>
        
        <div className={styles.warningText}>
          ⚠️ 权限不足，请查看其它页面
        </div>
        
        <div className={styles.descriptionText}>
          <Text>
            抱歉，您当前的账户权限无法访问此页面。<br />
            需访问此页面及其相关功能，请联系系统管理员为您分配相应权限。
          </Text>
        </div>
        
        <Button 
          type="primary" 
          size="large"
          icon={<HomeOutlined />}
          className={styles.actionButton}
          onClick={() => history.push('/welcome')}
        >
          返回首页
        </Button>
      </Space>
    </div>
  );
};

export default NoAccessPage; 