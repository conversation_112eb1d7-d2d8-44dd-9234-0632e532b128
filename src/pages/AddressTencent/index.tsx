import React, { useRef, useState, Suspense } from 'react';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Button, Upload, message, Modal, Space } from 'antd';
import { PlusOutlined, ImportOutlined, ExportOutlined, DeleteOutlined } from '@ant-design/icons';
import { TencentAddressRecord, PaginationParams, getTencentAddressList, exportTencentAddresses, importTencentAddresses, addTencentAddress, updateTencentAddress, removeTencentAddress } from '../../../supabase/address_tencent';
import UpdateForm from './components/Editor';
import CreateForm from './components/CreateForm';
import UploadProgress from './components/UploadProgress';
import * as XLSX from 'xlsx';

import { batchSyncStoresToTencent, StoreData } from '../../utils/tencentApi';

const AddressTencentList: React.FC = () => {
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, setUpdateModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<TencentAddressRecord>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<TencentAddressRecord[]>([]);
  const actionRef = useRef<ActionType>();
  const [currentParams, setCurrentParams] = useState<PaginationParams>({});
  
  // 进度条状态
  const [progressVisible, setProgressVisible] = useState<boolean>(false);
  const [dataImportProgress, setDataImportProgress] = useState<{
    current: number;
    total: number;
    status: 'normal' | 'active' | 'success' | 'exception';
    text: string;
  }>({
    current: 0,
    total: 0,
    status: 'normal',
    text: '',
  });
  const [tencentSyncProgress, setTencentSyncProgress] = useState<{
    current: number;
    total: number;
    status: 'normal' | 'active' | 'success' | 'exception';
    text: string;
  }>({
    current: 0,
    total: 0,
    status: 'normal',
    text: '',
  });
  const [overallStatus, setOverallStatus] = useState<'uploading' | 'importing' | 'syncing' | 'completed' | 'error'>('uploading');

  /**
   * 添加地址
   */
  const handleAdd = async (values: Partial<TencentAddressRecord>) => {
    try {
      // 1. 先将地址添加到数据库
      const result = await addTencentAddress(values as TencentAddressRecord);
      if (!result.success) {
        message.error('添加失败');
        return false;
      }

      message.success('地址添加成功');

      // 2. 同步到腾讯服务
      try {
        if (values.name && values.longitude !== undefined && values.latitude !== undefined && values.address) {
          message.loading('正在同步到腾讯服务...', 0); // 显示loading，不自动消失
          
          const storeData: StoreData = {
            name: values.name,
            lng: values.longitude,
            lat: values.latitude,
            addr: values.address,
            cpid: values.storeCodeId || ''
          };

          // 调用腾讯同步API
          await batchSyncStoresToTencent([storeData]);
          
          message.destroy(); // 清除loading
          message.success('地址添加和腾讯同步完成');
        } else {
          message.warning('地址信息不完整，跳过腾讯同步');
        }
      } catch (syncError) {
        message.destroy(); // 清除loading
        console.error('腾讯同步失败:', syncError);
        message.warning('地址添加成功，但腾讯同步失败，请稍后重试');
      }

      return true;
    } catch (error) {
      message.error('添加失败');
      return false;
    }
  };

  /**
   * 更新地址
   */
  const handleUpdate = async (id: number, values: Partial<TencentAddressRecord>) => {
    try {
      // 1. 先更新数据库中的地址
      // 确保移除created_at和updated_at字段
      const addressToUpdate = { ...values };
      delete addressToUpdate.created_at;
      delete addressToUpdate.updated_at;
      
      const result = await updateTencentAddress(id, addressToUpdate);
      if (!result.success) {
        message.error('更新失败');
        return false;
      }

      message.success('地址更新成功');

      // 2. 同步到腾讯服务
      try {
        if (values.name && values.longitude !== undefined && values.latitude !== undefined && values.address) {
          message.loading('正在同步到腾讯服务...', 0); // 显示loading，不自动消失
          
          const storeData: StoreData = {
            name: values.name,
            lng: values.longitude,
            lat: values.latitude,
            addr: values.address,
            cpid: values.storeCodeId || ''
          };

          // 调用腾讯同步API
          await batchSyncStoresToTencent([storeData]);
          
          message.destroy(); // 清除loading
          message.success('地址更新和腾讯同步完成');
        } else {
          message.warning('地址信息不完整，跳过腾讯同步');
        }
      } catch (syncError) {
        message.destroy(); // 清除loading
        console.error('腾讯同步失败:', syncError);
        message.warning('地址更新成功，但腾讯同步失败，请稍后重试');
      }

      return true;
    } catch (error) {
      message.error('更新失败');
      return false;
    }
  };

  /**
   * 删除地址
   */
  const handleRemove = async (id: number) => {
    try {
      const result = await removeTencentAddress(id);
      if (result.success) {
        message.success('删除成功');
        return true;
      }
      message.error('删除失败');
      return false;
    } catch (error) {
      message.error('删除失败');
      return false;
    }
  };

  /**
   * 处理导出
   * @param records 要导出的记录，不传则导出全部
   */
  const handleExport = async (records?: TencentAddressRecord[]) => {
    const hide = message.loading('正在导出');
    try {
      let dataToExport: TencentAddressRecord[] = [];
      
      if (records && records.length > 0) {
        dataToExport = records;
      } else {
        const response = await exportTencentAddresses();
        if (response.success && response.data) {
          dataToExport = response.data;
        } else {
          throw new Error('导出失败');
        }
      }
      
      // 字段映射关系（只保留腾讯格式）
      const tencentFieldMapping: Record<string, string> = {
        'marketName': '市场',
        'storeCodeId': '子点cpid(门店id)',
        'storeCodePoiid': '子点poiid',
        'cityName': '城市',
        'name': '子点名称',
        'address': '子点地址',
        'longitude': '子点x',
        'latitude': '子点y',
        'parentStoreCodePoiid': '主点poiid',
        'parentName': '主点名称',
        // 备注列后面单独处理
        'parentStoreCodeId': '母店号',
      };
      
      // 转换数据格式
      const tencentExcelData = dataToExport.map(item => {
        const rowData: Record<string, any> = {};
        Object.entries(tencentFieldMapping).forEach(([dbField, excelField]) => {
          rowData[excelField] = item[dbField as keyof TencentAddressRecord] !== undefined
            ? item[dbField as keyof TencentAddressRecord]
            : '';
        });
        rowData['备注'] = '已上线';
        return rowData;
      });
      
      // 使用xlsx库将数据转换为Excel格式
      const tencentWorksheet = XLSX.utils.json_to_sheet(tencentExcelData);
      
      // 定义列宽（可根据实际需求调整）
      tencentWorksheet['!cols'] = [
        { wch: 15 }, // 市场
        { wch: 20 }, // 子点cpid(门店id)
        { wch: 20 }, // 子点poiid
        { wch: 15 }, // 城市
        { wch: 30 }, // 子点名称
        { wch: 50 }, // 子点地址
        { wch: 15 }, // 子点x
        { wch: 15 }, // 子点y
        { wch: 20 }, // 主点poiid
        { wch: 30 }, // 主点名称
        { wch: 15 }, // 备注
        { wch: 20 }, // 母店号
      ];
      
      // 创建工作簿
      const tencentWorkbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(tencentWorkbook, tencentWorksheet, "地址数据");
      
      // 生成带时间戳的文件名
      const now = new Date();
      const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;
      const tencentFilename = `kfc_address_for_tencent_${timestamp}.xlsx`;
      XLSX.writeFile(tencentWorkbook, tencentFilename);
      
      hide();
      message.success('导出成功');
    } catch (error) {
      hide();
      message.error('导出失败，请重试');
    }
  };

  /**
   * 批量删除选中的地址
   */
  const handleBatchRemove = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的地址');
      return;
    }
    
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 条地址记录吗？`,
      onOk: async () => {
        const hide = message.loading('正在删除');
        try {
          const promises = selectedRowKeys.map(key => {
            const id = Number(key);
            return removeTencentAddress(id);
          });
          
          const results = await Promise.all(promises);
          const success = results.every(result => result.success);
          
          hide();
          if (success) {
            message.success('批量删除成功');
            setSelectedRowKeys([]);
            setSelectedRows([]);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          } else {
            message.error('部分删除失败，请刷新页面查看');
          }
        } catch (error) {
          hide();
          message.error('删除失败，请重试');
        }
      }
    });
  };

  /**
   * 导出选中的地址
   */
  const handleExportSelected = () => {
    if (selectedRows.length === 0) {
      message.warning('请先选择要导出的地址');
      return;
    }
    
    handleExport(selectedRows);
  };

  /**
   * 处理导入
   */
  const handleImport = async (file: File) => {
    try {
      // 显示进度对话框
      setProgressVisible(true);
      setOverallStatus('uploading');
      setDataImportProgress({ current: 0, total: 0, status: 'normal', text: '正在读取Excel文件...' });
      setTencentSyncProgress({ current: 0, total: 0, status: 'normal', text: '等待数据录入完成...' });

      // 读取Excel文件内容
      const reader = new FileReader();
      reader.readAsBinaryString(file);
      
      reader.onload = async (e) => {
        try {
          const binaryString = e.target?.result;
          const workbook = XLSX.read(binaryString, { type: 'binary' });
          
          // 假设Excel只有一个工作表，获取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // 将工作表转换为JSON对象
          const excelData = XLSX.utils.sheet_to_json(worksheet);
          
          if (Array.isArray(excelData) && excelData.length > 0) {
            // 字段映射关系
            const fieldMapping = {
              '子点cpid(门店id)': 'storeCodeId',
              '子点名称': 'name',
              '子点地址': 'address',
              '城市': 'cityName',
              '子点x': 'longitude',
              '子点y': 'latitude',
              '子点poiid': 'storeCodePoiid',
              '市场': 'marketName',
              '主点名称': 'parentName',
              '母店号': 'parentStoreCodeId',
              '主点poiid': 'parentStoreCodePoiid'
            };
            
            // 先检查Excel是否包含必要的列
            const requiredFields = ['子点cpid(门店id)', '子点名称', '子点地址', '城市', '子点x', '子点y'];
            const firstRow = excelData[0] as Record<string, any>;
            const missingColumns = requiredFields.filter(field => !(field in firstRow));
            
            if (missingColumns.length > 0) {
              throw new Error(`Excel文件结构有误，缺少必要的列: ${missingColumns.join(', ')}`);
            }
            
            // 处理基本数据
            const addresses: Partial<TencentAddressRecord>[] = [];
            
            let rowIndex = 0;
            for (const row of excelData as Record<string, any>[]) {
              rowIndex++;
              const address: Partial<TencentAddressRecord> = {};
              
              // 遍历映射关系，将Excel列映射到对应的字段
              Object.entries(fieldMapping).forEach(([excelField, dbField]) => {
                if (row[excelField] !== undefined) {
                  // 经纬度需要转换为数字类型
                  if (dbField === 'longitude' || dbField === 'latitude') {
                    address[dbField as keyof TencentAddressRecord] = Number(row[excelField]) as any;
                  } else {
                    address[dbField as keyof TencentAddressRecord] = row[excelField];
                  }
                }
              });
              
              // 确保必填字段都有值
              if (!address.storeCodeId || !address.name || !address.address || !address.cityName || 
                  address.longitude === undefined || address.latitude === undefined) {
                const missingFields = [];
                if (!address.storeCodeId) missingFields.push('子点cpid(门店id)');
                if (!address.name) missingFields.push('子点名称');
                if (!address.address) missingFields.push('子点地址');
                if (!address.cityName) missingFields.push('城市');
                if (address.longitude === undefined) missingFields.push('子点x(经度)');
                if (address.latitude === undefined) missingFields.push('子点y(纬度)');
                
                throw new Error(`Excel第${rowIndex}行缺少必要字段: ${missingFields.join(', ')}`);
              }
              
              addresses.push(address);
            }
            
            // 开始数据录入阶段
            setOverallStatus('importing');
            setDataImportProgress({ 
              current: 0, 
              total: addresses.length, 
              status: 'active', 
              text: '正在导入到数据库...' 
            });
            
            // 按批次导入数据到Supabase
            const batchSize = 30; // 每批次30条记录
            const batches = Math.ceil(addresses.length / batchSize);
            let successCount = 0;
            
            for (let i = 0; i < batches; i++) {
              const start = i * batchSize;
              const end = Math.min(start + batchSize, addresses.length);
              const batch = addresses.slice(start, end);
              
              try {
                await importTencentAddresses(batch as TencentAddressRecord[]);
                successCount += batch.length;
                
                // 更新进度
                const currentSuccessCount = successCount;
                setDataImportProgress(prev => ({
                  ...prev,
                  current: currentSuccessCount,
                  text: `正在导入到数据库... ${currentSuccessCount}/${addresses.length}`
                }));
              } catch (batchError) {
                console.error(`批次 ${i+1} 导入失败:`, batchError);
              }
            }
            
            // 数据录入完成
            setDataImportProgress({
              current: addresses.length,
              total: addresses.length,
              status: 'success',
              text: `数据录入完成 ${successCount}/${addresses.length}`
            });
            
            // 开始腾讯同步阶段
            setOverallStatus('syncing');
            setTencentSyncProgress({
              current: 0,
              total: addresses.length,
              status: 'active',
              text: '正在同步到腾讯服务...'
            });
            
            // 准备腾讯同步数据
            const tencentSyncData: StoreData[] = addresses.map(address => ({
              name: address.name || '',
              lng: address.longitude || 0,
              lat: address.latitude || 0,
              addr: address.address || '',
              cpid: address.storeCodeId || ''
            }));
            
            // 执行腾讯同步
            const tencentSyncCount = await batchSyncStoresToTencent(tencentSyncData, (completed, total, batchInfo) => {
              setTencentSyncProgress({
                current: completed,
                total: total,
                status: 'active',
                text: `正在同步到腾讯服务... ${batchInfo}`
              });
            });
            
            // 腾讯同步完成
            setTencentSyncProgress({
              current: addresses.length,
              total: addresses.length,
              status: 'success',
              text: `腾讯同步完成 ${tencentSyncCount}/${addresses.length}`
            });
            
            // 全部完成
            setOverallStatus('completed');
            
            if (actionRef.current) {
              actionRef.current.reload();
            }
          } else {
            throw new Error('Excel文件中没有数据');
          }
        } catch (parseError) {
          setOverallStatus('error');
          setDataImportProgress(prev => ({ ...prev, status: 'exception' }));
          setTencentSyncProgress(prev => ({ ...prev, status: 'exception' }));
          message.error(`导入失败: ${parseError instanceof Error ? parseError.message : '文件格式错误'}`);
        }
      };
      
      reader.onerror = () => {
        setOverallStatus('error');
        setDataImportProgress(prev => ({ ...prev, status: 'exception' }));
        setTencentSyncProgress(prev => ({ ...prev, status: 'exception' }));
        message.error('导入失败，无法读取文件');
      };
      
      return true;
    } catch (error) {
      setOverallStatus('error');
      setDataImportProgress(prev => ({ ...prev, status: 'exception' }));
      setTencentSyncProgress(prev => ({ ...prev, status: 'exception' }));
      message.error('导入失败，请重试');
      return false;
    }
  };

  /**
   * 上传前校验文件
   */
  const beforeUpload = (file: File) => {
    // 检查文件类型是否为Excel
    const isExcel = 
      file.type === 'application/vnd.ms-excel' || 
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.name.endsWith('.xlsx') || 
      file.name.endsWith('.xls');
      
    if (!isExcel) {
      message.error('只能上传Excel文件!');
      return false;
    }
    
    // 检查文件大小
    const isLt3M = file.size / 1024 / 1024 < 3;
    if (!isLt3M) {
      message.error('文件必须小于3MB!');
      return false;
    }
    
    Modal.confirm({
      title: '确认导入',
      content: '是否确认导入该文件？导入将覆盖现有数据。',
      onOk: () => handleImport(file),
    });
    
    return false;
  };

  const columns: ProColumns<TencentAddressRecord>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
    },
    {
      title: '门店id',
      dataIndex: 'storeCodeId',
      renderText: (val) => val,
      fieldProps: {
        style: { width: '100%' },
        placeholder: '请输入门店ID',
      },
    },
    {
      title: '名称',
      dataIndex: 'name',
      renderText: (val) => val,
      fieldProps: {
        style: { width: '100%' },
        placeholder: '请输入名称',
      },
    },
    {
      title: '地址',
      dataIndex: 'address',
      valueType: 'textarea',
      ellipsis: false,
      copyable: true,
      render: (_, record) => (
        <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
          {record.address}
        </div>
      ),
      fieldProps: {
        style: { width: '100%' },
        placeholder: '请输入地址',
      },
    },
    {
      title: '城市',
      dataIndex: 'cityName',
      hideInSearch: true,
    },
    {
      title: '电话',
      dataIndex: 'tel',
      hideInSearch: true,
    },
    {
      title: '经度',
      dataIndex: 'longitude',
      hideInSearch: true,
    },
    {
      title: '纬度',
      dataIndex: 'latitude',
      hideInSearch: true,
    },
    {
      title: '门店POIID',
      dataIndex: 'storeCodePoiid',
      hideInSearch: true,
    },
    {
      title: '市场',
      dataIndex: 'marketName',
      hideInSearch: true,
    },
    {
      title: '主点名称',
      dataIndex: 'parentName',
      hideInSearch: true,
    },
    {
      title: '母店号',
      dataIndex: 'parentStoreCodeId',
      hideInSearch: true,
    },
    {
      title: '主点POIID',
      dataIndex: 'parentStoreCodePoiid',
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '时间范围',
      key: 'timeRange',
      hideInTable: true,
      valueType: 'dateTimeRange',
      fieldProps: {
        placeholder: ['开始时间', '结束时间'],
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            setCurrentRow(record);
            setUpdateModalVisible(true);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除该地址吗？',
              onOk: async () => {
                if (record.id !== undefined) {
                  await handleRemove(record.id);
                  if (actionRef.current) {
                    actionRef.current.reload();
                  }
                } else {
                  message.error('删除失败，找不到ID');
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  // 选中行操作组件
  const TableAlertRender = () => {
    return (
      <Space size={16}>
        <span>已选 {selectedRowKeys.length} 项</span>
        <Button 
          type="primary" 
          danger 
          onClick={handleBatchRemove}
        >
          <DeleteOutlined /> 删除选中
        </Button>
        <Button 
          type="primary"
          onClick={handleExportSelected}
        >
          <ExportOutlined /> 导出选中
        </Button>
        <Button 
          onClick={() => {
            setSelectedRowKeys([]);
            setSelectedRows([]);
          }}
        >
          取消选择
        </Button>
      </Space>
    );
  };

  return (
    <PageContainer>
      <ProTable<TencentAddressRecord>
        headerTitle="地址列表(腾讯)"
        actionRef={actionRef}
        rowKey="id"
        tableClassName="address-tencent-table"
        columnsState={{
          defaultValue: {
            id: { show: false },
            storeCodeId: { show: true },
            name: { show: true },
            address: { show: true },
            cityName: { show: true },
            tel: { show: true },
            longitude: { show: true },
            latitude: { show: true },
            storeCodePoiid: { show: false },
            marketName: { show: false },
            parentName: { show: false },
            parentStoreCodeId: { show: false },
            parentStoreCodePoiid: { show: false },
            updated_at: { show: true },
            option: { show: true },
          }
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows);
          },
        }}
        tableAlertRender={({ selectedRowKeys }) => selectedRowKeys.length > 0 ? <TableAlertRender /> : false}
        tableAlertOptionRender={false}
        search={{
          labelWidth: 100,
          defaultCollapsed: true,
          optionRender: (searchConfig) => [
            <Button 
              key="reset" 
              style={{ marginRight: 8 }}
              onClick={() => {
                searchConfig?.form?.resetFields();
                searchConfig?.form?.submit();
              }}
            >
              重置
            </Button>,
            <Button 
              key="submit" 
              type="primary" 
              onClick={() => searchConfig?.form?.submit()}
            >
              查询
            </Button>,
          ],
          span: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 6,
            xl: 6,
            xxl: 6,
          },
          layout: 'horizontal',
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => setCreateModalVisible(true)}
          >
            <PlusOutlined /> 新建
          </Button>,
          <Upload 
            key="import" 
            name="file" 
            showUploadList={false}
            beforeUpload={beforeUpload}
            disabled={selectedRowKeys.length > 0}
          >
            <Button disabled={selectedRowKeys.length > 0}>
              <ImportOutlined /> 导入
            </Button>
          </Upload>,
          <Button
            key="export"
            disabled={selectedRowKeys.length > 0}
            onClick={() => {
              // 显示导出过程的提示
              const hide = message.loading('正在获取当前筛选数据...');
              
              // 使用存储的当前查询参数，但设置一个大的pageSize
              const exportParams = { 
                ...currentParams, 
                current: 1, 
                pageSize: 100000 // 设置一个很大的页面大小以获取所有匹配记录
              };
              
              getTencentAddressList(exportParams).then(result => {
                hide();
                if (result.success && result.data) {
                  handleExport(result.data);
                } else {
                  message.error('获取数据失败，请重试');
                }
              }).catch(() => {
                hide();
                message.error('获取数据失败，请重试');
              });
            }}
          >
            <ExportOutlined /> 导出
          </Button>,
        ]}
        request={async (params) => {
          // 构造请求参数
          const requestParams: PaginationParams = {
            current: params.current,
            pageSize: params.pageSize,
          };
          
          // 搜索参数分别传递
          if (params.name) {
            requestParams.name = params.name;
          }
          if (params.storeCodeId) {
            requestParams.storeCodeId = params.storeCodeId;
          }
          if (params.address) {
            requestParams.address = params.address;
          }
          if (params.cityName) {
            requestParams.cityName = params.cityName;
          }
          if (params.tel) {
            requestParams.tel = params.tel;
          }
          if (params.marketName) {
            requestParams.marketName = params.marketName;
          }
          if (params.parentName) {
            requestParams.parentName = params.parentName;
          }
          if (params.parentStoreCodeId) {
            requestParams.parentStoreCodeId = params.parentStoreCodeId;
          }
          
          // 处理时间范围参数
          if (params.timeRange && params.timeRange.length === 2) {
            const [startTime, endTime] = params.timeRange;
            if (startTime) {
              requestParams.startTime = startTime;
            }
            if (endTime) {
              requestParams.endTime = endTime;
            }
          }
          
          // 存储当前请求参数，用于导出功能
          setCurrentParams(requestParams);
          
          const res = await getTencentAddressList(requestParams);
          return {
            data: res.data || [],
            success: res.success,
            total: 'total' in res ? res.total : 0,
          };
        }}
        columns={columns}
        pagination={{
          showQuickJumper: true,
        }}
      />

      <Suspense fallback={<div>加载中...</div>}>
        {createModalVisible && (
          <CreateForm
            visible={createModalVisible}
            onCancel={() => setCreateModalVisible(false)}
            onSubmit={async (values: Partial<TencentAddressRecord>) => {
              await handleAdd(values);
              setCreateModalVisible(false);
              if (actionRef.current) {
                actionRef.current.reload();
              }
            }}
          />
        )}

        {updateModalVisible && currentRow && (
          <UpdateForm
            visible={updateModalVisible}
            values={currentRow as any}
            onCancel={() => setUpdateModalVisible(false)}
            onSubmit={async (values: Partial<TencentAddressRecord>) => {
              if (currentRow.id) {
                await handleUpdate(currentRow.id, values);
                setUpdateModalVisible(false);
                if (actionRef.current) {
                  actionRef.current.reload();
                }
              }
            }}
          />
        )}
      </Suspense>

      {/* 上传进度对话框 */}
      <UploadProgress
        visible={progressVisible}
        onCancel={() => setProgressVisible(false)}
        dataImportProgress={dataImportProgress}
        tencentSyncProgress={tencentSyncProgress}
        overallStatus={overallStatus}
      />
    </PageContainer>
  );
};

export default AddressTencentList; 