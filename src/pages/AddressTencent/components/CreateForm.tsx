import React from 'react';
import { Modal, Form, Input, InputNumber } from 'antd';
import { TencentAddressRecord } from '../../../../supabase/address_tencent';

export interface CreateFormProps {
  visible: boolean;
  onSubmit: (values: Partial<TencentAddressRecord>) => void;
  onCancel: () => void;
}

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { visible, onSubmit, onCancel } = props;
  const [form] = Form.useForm();

  // 处理提交
  const handleSubmit = async () => {
    try {
      const fieldsValue = await form.validateFields();
      form.resetFields();
      onSubmit(fieldsValue);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  React.useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [form, visible]);

  return (
    <Modal
      title="创建地址"
      visible={visible}
      width={800}
      onOk={handleSubmit}
      onCancel={onCancel}
      destroyOnClose
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
      >
        <Form.Item
          name="storeCodeId"
          label="门店ID"
          rules={[{ required: true, message: '请输入门店ID' }]}
        >
          <Input placeholder="请输入门店ID" />
        </Form.Item>
        
        <Form.Item
          name="name"
          label="名称"
          rules={[{ required: true, message: '请输入名称' }]}
        >
          <Input placeholder="请输入名称" />
        </Form.Item>
        
        <Form.Item
          name="address"
          label="地址"
          rules={[{ required: true, message: '请输入地址' }]}
        >
          <Input.TextArea rows={3} placeholder="请输入地址" />
        </Form.Item>
        
        <Form.Item
          name="cityName"
          label="城市"
          rules={[{ required: true, message: '请输入城市' }]}
        >
          <Input placeholder="请输入城市" />
        </Form.Item>
        
        <Form.Item
          name="tel"
          label="电话"
        >
          <Input placeholder="请输入电话" />
        </Form.Item>
        
        <Form.Item
          name="longitude"
          label="经度"
          rules={[{ required: true, message: '请输入经度' }]}
        >
          <InputNumber
            style={{ width: '100%' }}
            placeholder="请输入经度"
            step={0.000001}
            precision={6}
          />
        </Form.Item>
        
        <Form.Item
          name="latitude"
          label="纬度"
          rules={[{ required: true, message: '请输入纬度' }]}
        >
          <InputNumber
            style={{ width: '100%' }}
            placeholder="请输入纬度"
            step={0.000001}
            precision={6}
          />
        </Form.Item>
        
        <Form.Item
          name="storeCodePoiid"
          label="门店POIID"
        >
          <Input placeholder="请输入门店POIID" />
        </Form.Item>
        
        <Form.Item
          name="marketName"
          label="市场"
        >
          <Input placeholder="请输入市场" />
        </Form.Item>
        
        <Form.Item
          name="parentName"
          label="主点名称"
        >
          <Input placeholder="请输入主点名称" />
        </Form.Item>
        
        <Form.Item
          name="parentStoreCodeId"
          label="母店号"
        >
          <Input placeholder="请输入母店号" />
        </Form.Item>
        
        <Form.Item
          name="parentStoreCodePoiid"
          label="主点POIID"
        >
          <Input placeholder="请输入主点POIID" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateForm; 