import React, { useEffect } from 'react';
import { ModalForm, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import { Form } from 'antd';
import type { UserRecord } from '../../../services/ant-design-pro/users';

interface EditFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: Partial<UserRecord>) => Promise<void>;
  initialValues?: Partial<UserRecord>;
}

const EditForm: React.FC<EditFormProps> = ({ visible, onCancel, onSubmit, initialValues }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [visible, initialValues, form]);

  const handleSubmit = async (values: any) => {
    await onSubmit(values);
    form.resetFields();
  };

  return (
    <ModalForm
      title="编辑用户"
      form={form}
      open={visible}
      onOpenChange={(open) => !open && onCancel()}
      onFinish={handleSubmit}
      width={520}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
    >
      <ProFormText
        name="name"
        label="用户昵称"
        placeholder="请输入用户昵称"
        rules={[
          {
            required: true,
            message: '请输入用户昵称',
          },
        ]}
      />

      <ProFormSelect
        name="group"
        label="用户所属组"
        placeholder="请选择用户所属组"
        options={[
          { label: '用户团队', value: '用户团队' },
          { label: '管理团队', value: '管理团队' },
        ]}
        rules={[
          {
            required: true,
            message: '请选择用户所属组',
          },
        ]}
      />

      <ProFormSelect
        name="ant_design_role"
        label="用户角色"
        placeholder="请选择用户角色"
        options={[
          { label: '普通用户', value: 'user' },
          { label: '管理员', value: 'admin' },
        ]}
        rules={[
          {
            required: true,
            message: '请选择用户角色',
          },
        ]}
      />

      <ProFormText
        name="phone"
        label="手机号"
        placeholder="请输入手机号"
      />
    </ModalForm>
  );
};

export default EditForm; 