import React from 'react';
import { ModalForm, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import { Form } from 'antd';

interface CreateFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: {
    username: string;
    password: string;
    name: string;
    group?: string;
    ant_design_role?: string;
  }) => Promise<void>;
}

const CreateForm: React.FC<CreateFormProps> = ({ visible, onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  const handleSubmit = async (values: any) => {
    await onSubmit(values);
    form.resetFields();
  };

  return (
    <ModalForm
      title="新增用户"
      form={form}
      open={visible}
      onOpenChange={(open) => !open && onCancel()}
      onFinish={handleSubmit}
      width={520}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
    >
      <ProFormText
        name="username"
        label="用户名"
        placeholder="请输入用户名"
        fieldProps={{
          autoComplete: 'off',
        }}
        rules={[
          {
            required: true,
            message: '请输入用户名',
          },
          {
            pattern: /^[a-zA-Z0-9_]{3,20}$/,
            message: '用户名只能包含字母、数字、下划线，长度3-20位',
          },
        ]}
      />
      
      <ProFormText.Password
        name="password"
        label="密码"
        placeholder="请输入密码"
        fieldProps={{
          autoComplete: 'new-password',
        }}
        rules={[
          {
            required: true,
            message: '请输入密码',
          },
          {
            min: 6,
            message: '密码至少6位',
          },
        ]}
      />
      
      <ProFormText
        name="name"
        label="用户昵称"
        placeholder="请输入用户昵称"
        rules={[
          {
            required: true,
            message: '请输入用户昵称',
          },
        ]}
      />

      <ProFormSelect
        name="group"
        label="用户所属组"
        placeholder="请选择用户所属组"
        options={[
          { label: '用户团队', value: '用户团队' },
          { label: '管理团队', value: '管理团队' },
        ]}
        rules={[
          {
            required: true,
            message: '请选择用户所属组',
          },
        ]}
      />

      <ProFormSelect
        name="ant_design_role"
        label="用户角色"
        placeholder="请选择用户角色"
        options={[
          { label: '普通用户', value: 'user' },
          { label: '管理员', value: 'admin' },
        ]}
        rules={[
          {
            required: true,
            message: '请选择用户角色',
          },
        ]}
      />
    </ModalForm>
  );
};

export default CreateForm; 