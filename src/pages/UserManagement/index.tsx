import React, { useRef, useState } from 'react';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Button, message, Modal, Tag, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { 
  queryUsers, 
  createUser, 
  updateUserInfo, 
  deleteUser,
  type UserRecord, 
  type UserQueryParams 
} from '../../services/ant-design-pro/users';
import CreateForm from './components/CreateForm';
import EditForm from './components/EditForm';

/**
 * 用户管理页面
 */
const UserManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  
  // 表单状态
  const [createVisible, setCreateVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<UserRecord | undefined>();


  // 处理新增
  const handleAdd = async (values: {
    username: string;
    password: string;
    name: string;
    group?: string;
    ant_design_role?: string;
  }) => {
    try {
      // 将用户名转换为邮箱格式
      const userData = {
        ...values,
        email: `${values.username}@163.com`,
      };
      delete (userData as any).username; // 移除username字段

      const result = await createUser(userData);
      if (result.success) {
        message.success('用户创建成功');
        setCreateVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(result.message || '创建失败');
      }
    } catch (error) {
      message.error('创建失败');
    }
  };

  // 处理编辑
  const handleEdit = (record: UserRecord) => {
    setCurrentRecord(record);
    setEditVisible(true);
  };

  // 处理更新
  const handleUpdate = async (values: Partial<UserRecord>) => {
    console.log('更新用户，当前记录:', currentRecord);
    console.log('当前记录的所有字段:', currentRecord ? Object.keys(currentRecord) : 'null');
    console.log('更新数据:', values);
    
    if (!currentRecord) {
      message.error('当前用户记录不存在');
      console.error('currentRecord 为空');
      return;
    }
    
    // 获取用户ID
    const userId = currentRecord.user_id;
    console.log('解析得到的userId:', userId);
    
    if (!userId) {
      message.error('当前用户ID不存在');
      console.error('缺少用户ID，记录:', currentRecord);
      return;
    }
    
    try {
      const result = await updateUserInfo(userId, values);
      console.log('更新结果:', result);
      
      if (result.success) {
        message.success('用户更新成功');
        setEditVisible(false);
        setCurrentRecord(undefined);
        actionRef.current?.reload();
      } else {
        message.error(result.message || '更新失败');
        console.error('更新失败:', result);
      }
    } catch (error) {
      message.error('更新操作异常');
      console.error('更新异常:', error);
    }
  };

  // 处理删除
  const handleDelete = (record: UserRecord) => {
    console.log('删除用户记录:', record);
    console.log('记录的所有字段:', Object.keys(record));
    
    // 获取用户ID
    const userId = record.user_id;
    
    console.log('解析得到的userId:', userId);
    
    if (!userId) {
      message.error('用户ID不存在，无法删除');
      console.error('缺少用户ID，记录:', record);
      return;
    }

    if (record.ant_design_role === 'admin') {
      message.error('不能删除管理员用户');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除用户 "${record.name}" 吗？此操作不可撤销。`,
      onOk: async () => {
        try {
          console.log('执行删除，用户ID:', userId);
          const result = await deleteUser(userId);
          if (result.success) {
            message.success('用户删除成功');
            actionRef.current?.reload();
          } else {
            message.error(result.message || '删除失败');
            console.error('删除失败:', result);
          }
        } catch (error) {
          message.error('删除操作异常');
          console.error('删除异常:', error);
        }
      },
    });
  };

  // 表格列定义
  const columns: ProColumns<UserRecord>[] = [
    {
      title: '用户昵称',
      dataIndex: 'name',
      width: 120,
      align: 'center',
      search: {
        transform: (value) => ({ name: value }),
      },
    },
    {
      title: '登录用户名',
      dataIndex: 'email',
      width: 200,
      align: 'center',
      search: {
        transform: (value) => ({ email: value }),
      },
      render: (text) => {
        if (!text) return '-';
        // 去掉@163.com后缀
        return typeof text === 'string' ? text.replace('@163.com', '') : text;
      },
    },
    {
      title: '用户所属组',
      dataIndex: 'group',
      width: 150,
      align: 'center',
      search: {
        transform: (value) => ({ group: value }),
      },
    },
    {
      title: '用户角色',
      dataIndex: 'ant_design_role',
      width: 100,
      align: 'center',
      render: (text) => {
        const isAdmin = text === 'admin';
        return (
          <Tag color={isAdmin ? 'red' : 'blue'}>
            {isAdmin ? '管理员' : '普通用户'}
          </Tag>
        );
      },
      filters: [
        {
          text: '管理员',
          value: 'admin',
        },
        {
          text: '普通用户',
          value: 'user',
        },
      ],
      onFilter: (value, record) => {
        return record.ant_design_role === value;
      },
    },
    {
      title: '最后登录时间',
      dataIndex: 'last_sign_in_at',
      width: 180,
      align: 'center',
      search: false,
      render: (text) => {
        if (!text) {
          return '-';
        }
        
        try {
          const date = new Date(text as string);
          // 检查日期是否有效
          if (isNaN(date.getTime())) {
            return '-';
          }
          return date.toLocaleString('zh-CN');
        } catch (error) {
          return '-';
        }
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        console.log('操作列render，当前记录:', record);
        return [
          <Tooltip key="edit" title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                console.log('点击编辑按钮，记录:', record);
                handleEdit(record);
              }}
            />
          </Tooltip>,
          <Tooltip key="delete" title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={record.ant_design_role === 'admin' || !record.user_id}
              onClick={() => {
                console.log('点击删除按钮，记录:', record);
                handleDelete(record);
              }}
            />
          </Tooltip>,
        ];
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: '用户管理',
        breadcrumb: {},
      }}
    >
      <ProTable<UserRecord, UserQueryParams>
        headerTitle="用户列表"
        actionRef={actionRef}
        rowKey={(record) => {
          console.log('ProTable rowKey 函数调用，记录:', record);
          return record.id || record.user_id || Math.random().toString();
        }}
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateVisible(true)}
          >
            新增用户
          </Button>,
        ]}
        request={async (params) => {
          console.log('请求用户列表，参数:', params);
          const result = await queryUsers({
            pageNo: params.current || 1,
            pageSize: params.pageSize || 20,
            name: params.name,
            email: params.email,
            group: params.group,
            ant_design_role: params.ant_design_role,
          });
          
          console.log('用户列表API返回结果:', result);
          console.log('用户列表数据:', result.data);
          
          // 检查每个用户记录的id字段
          if (result.data && result.data.length > 0) {
            console.log('第一个用户记录:', result.data[0]);
            console.log('第一个用户的id字段:', result.data[0].id);
            console.log('第一个用户的user_id字段:', result.data[0].user_id);
          }
          
          return {
            data: result.data || [],
            success: result.success,
            total: result.total || 0,
          };
        }}
        columns={columns}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
        scroll={{ x: 1200 }}
      />

      {/* 新增用户表单 */}
      <CreateForm
        visible={createVisible}
        onCancel={() => setCreateVisible(false)}
        onSubmit={handleAdd}
      />

      {/* 编辑用户表单 */}
      <EditForm
        visible={editVisible}
        onCancel={() => {
          setEditVisible(false);
          setCurrentRecord(undefined);
        }}
        onSubmit={handleUpdate}
        initialValues={currentRecord}
      />
    </PageContainer>
  );
};

export default UserManagement; 