/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};
  return {
    canAdmin: currentUser && currentUser.access === 'admin',
    // 地址管理权限：只有管理员才能访问地址管理页面
    canManageAddress: currentUser && currentUser.access === 'admin',
    // 用户管理权限：只有管理员才能访问用户管理页面
    canManageUsers: currentUser && currentUser.access === 'admin',
  };
}
