// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

// 地址信息数据类型
export interface AddressItem {
  id: number;
  name: string;
  storeCodeId: string;
  address: string;
  cityName: string;
  tel: string;
  longitude: number;
  latitude: number;
}

export interface AddressParams {
  // 查询关键字
  keyword?: string;
  // 名称
  name?: string;
  // 门店ID
  storeCodeId?: string;
  // 地址
  address?: string;
  // 城市
  cityName?: string;
  // 电话
  tel?: string;
  // 分页参数
  current?: number;
  pageSize?: number;
}

// 获取地址列表
export async function getAddressList(params: AddressParams) {
  return request<{
    data: AddressItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  }>('/api/address/list', {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

// 添加地址
export async function addAddress(params: AddressItem) {
  return request<AddressItem>('/api/address/add', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 更新地址
export async function updateAddress(params: AddressItem) {
  return request<AddressItem>('/api/address/update', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 删除地址
export async function removeAddress(params: { ids: number[] }) {
  return request<Record<string, any>>('/api/address/remove', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 导出地址
export async function exportAddress(params?: AddressParams) {
  return request('/api/address/export', {
    method: 'GET',
    params: {
      ...params,
    },
    responseType: 'blob',
  });
}

// 导入地址
export async function importAddress(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return request('/api/address/import', {
    method: 'POST',
    data: formData,
  });
} 