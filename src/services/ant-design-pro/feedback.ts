import { request } from '@umijs/max';
import CryptoJS from 'crypto-js';

// 定义反馈记录的数据类型
export type FeedbackRecord = {
  id: number;
  phone: string;
  type: string;
  content: string;
  imgUrl: string;
  videoUrl: string;
  lastupdatetime: number;
  sessionId: string;
  userCode: string;
};

// 定义查询参数类型
export type FeedbackQueryParams = {
  pageNo: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
  phone?: string;
};

// 定义API响应类型
export type FeedbackQueryResult = {
  data: {
    total: number;
    pageNo: number;
    totalPage: number;
    hasMore: boolean;
    pageSize: number;
    list: FeedbackRecord[];
  };
  errCode: number;
  errorCode: string;
};

// 请求头签名加密
const encryptHeader = (opt: {
  path: string;
  url: string;
  method: string;
  params?: any;
}) => {
  const client_key = 'kbaptkJGtByWrQaP';
  const client_sec = 'jkJrRIacBwcpK68d';

  const kbcts = new Date().getTime();
  let signStr = `${client_key}\t${client_sec}\t${kbcts}\t${opt.path}`;
  const queryStr = opt.url.split('?')[1] || '';

  if (opt.method === 'POST') {
    signStr = `${signStr}\t\t${JSON.stringify(opt.params || {})}`;
  } else {
    signStr = `${signStr}\t${queryStr}`;
  }

  const md5Hash = CryptoJS.MD5(signStr).toString();

  return {
    'Cache-Control': 'no-cache',
    kbcts: kbcts.toString(),
    kbck: client_key,
    kbsv: md5Hash,
  };
};

/**
 * 查询AI点餐反馈统计
 */
export async function queryFeedback(params: FeedbackQueryParams, isProduction: boolean = true) {
  const signPath = '/aiagent/feedback/query'; // 用于签名计算的路径
  const fullPath = '/agent-service/aiagent/feedback/query'; // 完整的API路径
  
  // 根据环境选择不同的baseUrl
  const baseUrl = isProduction 
    ? 'https://aiordering.kfc.com.cn'  // 生产环境
    : 'https://tappagent.kfc.com.cn';  // 测试环境
    
  const fullUrl = `${baseUrl}${fullPath}`;

  // 构建POST请求参数
  const requestParams = {
    business: 'preorder',
    brand: 'KFC_PRE',
    agentService: 'ali',
    portalSource: 'aiordering',
    deviceId: '51055aac3d225643d7d314269adae023',
    pageNo: params.pageNo,
    pageSize: params.pageSize,
    ...(params.startTime && { startTime: params.startTime }),
    ...(params.endTime && { endTime: params.endTime }),
    ...(params.phone && { phone: params.phone }),
  };

  // 生成签名头
  const headers = encryptHeader({
    path: signPath,
    url: fullUrl,
    method: 'POST',
    params: requestParams,
  });

  return request<FeedbackQueryResult>(fullUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    data: requestParams,
  });
} 