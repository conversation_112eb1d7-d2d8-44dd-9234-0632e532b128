import {
  getUserList,
  addUser,
  updateUser,
  removeUser,
  getCurrentUserId,
  type UserRecord,
  type UserQueryParams,
  type UserListResponse,
} from '../../../supabase/users';

/**
 * 获取用户列表
 */
export async function queryUsers(params: UserQueryParams): Promise<UserListResponse> {
  return await getUserList(params);
}

/**
 * 添加用户
 */
export async function createUser(userData: {
  email: string;
  password: string;
  name: string;
  group?: string;
  ant_design_role?: string;
}): Promise<any> {
  return await addUser(userData);
}

/**
 * 更新用户
 */
export async function updateUserInfo(userId: string, userData: Partial<UserRecord>): Promise<any> {
  return await updateUser(userId, userData);
}

/**
 * 删除用户
 */
export async function deleteUser(userId: string): Promise<any> {
  return await removeUser(userId);
}

/**
 * 获取当前用户ID
 */
export async function getCurrentUserIdApi(): Promise<string | null> {
  return await getCurrentUserId();
}

// 导出类型
export type { UserRecord, UserQueryParams, UserListResponse }; 