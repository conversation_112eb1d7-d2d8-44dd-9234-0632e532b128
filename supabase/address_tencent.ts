import { supabaseClient, supabaseAdmin, handleSupabaseError } from './index';

// 表名常量
export const TENCENT_ADDRESSES = 'address_tencent';

// 地址记录类型定义，与address.ts保持一致
export interface TencentAddressRecord {
  id?: number;
  name: string;
  storeCodeId: string;
  address: string;
  cityName: string;
  tel?: string;
  longitude: number;
  latitude: number;
  storeCodePoiid?: string;
  marketName?: string;
  parentName?: string;
  parentStoreCodeId?: string;
  parentStoreCodePoiid?: string;
  created_at?: string;
  updated_at?: string;
}

// 分页参数类型，与address.ts保持一致
export interface PaginationParams {
  current?: number;
  pageSize?: number;
  name?: string;
  storeCodeId?: string;
  address?: string;
  cityName?: string;
  tel?: string;
  marketName?: string;
  parentName?: string;
  parentStoreCodeId?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 获取地址列表，支持分页和关键词搜索
 */
export async function getTencentAddressList({ 
  current = 1, 
  pageSize = 10,
  name,
  storeCodeId,
  address,
  cityName,
  tel,
  marketName,
  parentName,
  parentStoreCodeId,
  startTime,
  endTime
}: PaginationParams = {}) {
  try {
    // 计算分页范围
    const from = (current - 1) * pageSize;
    const to = from + pageSize - 1;

    // 构建基础查询
    let query = supabaseClient
      .from(TENCENT_ADDRESSES)
      .select('*', { count: 'exact' });

    // 添加搜索条件
    if (name) {
      query = query.ilike('name', `%${name}%`);
    }
    
    if (storeCodeId) {
      query = query.ilike('storeCodeId', `%${storeCodeId}%`);
    }
    
    if (address) {
      query = query.ilike('address', `%${address}%`);
    }
    
    if (cityName) {
      query = query.ilike('cityName', `%${cityName}%`);
    }
    
    if (tel) {
      query = query.ilike('tel', `%${tel}%`);
    }
    
    if (marketName) {
      query = query.ilike('marketName', `%${marketName}%`);
    }
    
    if (parentName) {
      query = query.ilike('parentName', `%${parentName}%`);
    }
    
    if (parentStoreCodeId) {
      query = query.ilike('parentStoreCodeId', `%${parentStoreCodeId}%`);
    }
    
    // 添加时间范围筛选
    if (startTime) {
      query = query.gte('updated_at', startTime);
    }
    
    if (endTime) {
      query = query.lte('updated_at', endTime);
    }

    // 执行分页查询
    const { data, error, count } = await query
      .order('updated_at', { ascending: false })
      .range(from, to);

    if (error) {
      throw error;
    }

    return {
      data,
      total: count || 0,
      success: true,
      pageSize,
      current,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 导出所有地址记录
 */
export async function exportTencentAddresses() {
  try {
    const { data, error } = await supabaseClient
      .from(TENCENT_ADDRESSES)
      .select('*')
      .order('updated_at', { ascending: false });

    if (error) {
      throw error;
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 导入地址记录
 */
export async function importTencentAddresses(addresses: TencentAddressRecord[]) {
  try {
    const { data, error } = await supabaseAdmin
      .from(TENCENT_ADDRESSES)
      .upsert(addresses, { onConflict: 'storeCodeId' })
      .select();

    if (error) {
      throw error;
    }

    return {
      success: true,
      message: '导入成功',
      data,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 添加地址记录
 */
export async function addTencentAddress(address: TencentAddressRecord) {
  try {
    const { data, error } = await supabaseAdmin
      .from(TENCENT_ADDRESSES)
      .insert(address)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 更新地址记录
 */
export async function updateTencentAddress(id: number, address: Partial<TencentAddressRecord>) {
  try {
    // 删除时间戳字段，避免冲突
    const addressToUpdate = { ...address };
    delete addressToUpdate.created_at;
    delete addressToUpdate.updated_at;
    
    const { data, error } = await supabaseAdmin
      .from(TENCENT_ADDRESSES)
      .update(addressToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('更新地址失败:', error);
      throw error;
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 删除地址记录
 */
export async function removeTencentAddress(id: number) {
  try {
    const { error } = await supabaseAdmin
      .from(TENCENT_ADDRESSES)
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return {
      success: true,
      data: { id },
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
} 