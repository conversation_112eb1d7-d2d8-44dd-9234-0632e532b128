# Supabase 集成

本目录包含与 Supabase 集成相关的代码和配置。

## 配置信息

Supabase 服务配置：
- 部署地址：http://172.20.52.217:8000
- 相关密钥存储在项目根目录的 `.env` 文件中

## 环境变量

项目使用根目录下的 `.env` 文件中的以下环境变量：

```
SUPABASE_URL=http://172.20.52.217:8000
SUPABASE_ANON_KEY=你的匿名密钥
SUPABASE_SERVICE_KEY=你的服务角色密钥
```

### 环境变量加载说明

当前项目已经配置了双重保障机制来确保 Supabase 能够正常运行：

1. 首先尝试从 `process.env` 中读取环境变量，这需要确保环境变量已被正确加载
2. 如果环境变量不可用，则使用 `supabase/index.ts` 中配置的默认值作为备用

对于开发环境，可以直接使用备用值，但对于生产环境，强烈建议正确配置和加载环境变量。

如果你需要确保环境变量被正确加载，可以考虑以下解决方案：

- 使用 `dotenv` 库在脚本启动时手动加载环境变量
- 在启动脚本前使用命令行导出环境变量 (例如: `export SUPABASE_URL=你的URL`)
- 在 CI/CD 流程中设置环境变量

### 测试环境变量加载

环境变量的正确加载对于 Supabase 集成至关重要。你可以通过以下命令来测试环境变量是否正确加载：

```bash
# 运行环境变量测试脚本
npx ts-node supabase/scripts/test-env.ts
```

## 文件结构

- `index.ts` - Supabase 客户端初始化和通用功能
- `address.ts` - 地址管理相关的 CRUD 操作
- `env-check.ts` - 环境变量检查工具
- `scripts/` - 数据库初始化脚本和其他工具
  - `init-db.ts` - 数据库初始化脚本
  - `mock-data.ts` - 测试数据
  - `test-env.ts` - 环境变量测试脚本

## 数据表结构

### 地址表 (addresses)

| 字段名      | 类型        | 描述         |
|------------|------------|--------------|
| id         | int8       | 主键，自增     |
| name       | text       | 地址名称      |
| storeCode  | text       | 门店代码      |
| address    | text       | 详细地址      |
| city       | text       | 城市名        |
| phone      | text       | 联系电话      |
| longitude  | float8     | 经度          |
| latitude   | float8     | 纬度          |
| created_at | timestamp  | 创建时间      |
| updated_at | timestamp  | 更新时间      |

## 使用方法

### 初始化数据库

```bash
# 确保已安装依赖
npm install

# 运行初始化脚本
npx ts-node supabase/scripts/init-db.ts
```

### 在代码中使用

```typescript
// 引入需要的函数
import { getAddressList, addAddress, updateAddress, removeAddress } from '@/supabase/address';

// 获取地址列表
const fetchAddresses = async () => {
  const result = await getAddressList({ current: 1, pageSize: 10, keyword: '北京' });
  if (result.success) {
    console.log('地址列表:', result.data);
  }
};

// 添加新地址
const createAddress = async () => {
  const newAddress = {
    name: '新门店',
    storeCode: 'NEW001',
    address: '北京市海淀区某某路123号',
    city: '北京',
    phone: '13800138000',
    longitude: 116.3,
    latitude: 39.9
  };
  
  const result = await addAddress(newAddress);
  if (result.success) {
    console.log('新地址已添加:', result.data);
  }
};
``` 