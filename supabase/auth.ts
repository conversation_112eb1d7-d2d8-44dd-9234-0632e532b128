import { supabaseClient, TABLES, handleSupabaseError } from './index';

/**
 * 用户登录
 * @param email 用户邮箱
 * @param password 用户密码
 */
export const signIn = async (email: string, password: string) => {
  try {
    const { data, error } = await supabaseClient.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    if (data?.user) {
      // 获取用户详细信息（角色等）
      const { data: profileData, error: profileError } = await supabaseClient
        .from(TABLES.USER_PROFILES)
        .select('*')
        .eq('user_id', data.user.id)
        .single();

      if (profileError) throw profileError;

      return {
        success: true,
        status: 'ok',
        type: 'account',
        currentAuthority: profileData?.ant_design_role || 'user',
        user: {
          id: data.user.id,
          email: data.user.email,
          role: profileData?.ant_design_role,
        },
      };
    }

    return {
      success: false,
      status: 'error',
      message: '登录失败',
    };
  } catch (error) {
    return {
      ...handleSupabaseError(error),
      status: 'error',
      type: 'account',
      currentAuthority: 'guest',
    };
  }
};

/**
 * 获取当前登录用户信息
 */
export const getCurrentUser = async () => {
  try {
    const { data: sessionData, error: sessionError } = await supabaseClient.auth.getSession();
    
    if (sessionError) throw sessionError;
    
    if (!sessionData.session) {
      return {
        success: false,
        message: '未登录',
        data: {
          isLogin: false,
        },
      };
    }
    
    const { data: userData, error: userError } = await supabaseClient.auth.getUser();
    
    if (userError) throw userError;
    
    // 获取用户详细信息（角色等）
    const { data: profileData, error: profileError } = await supabaseClient
      .from(TABLES.USER_PROFILES)
      .select('*')
      .eq('user_id', userData.user.id)
      .single();
      
    if (profileError && profileError.code !== 'PGRST116') throw profileError;
    
    return {
      success: true,
      data: {
        name: profileData?.name || userData.user.email?.split('@')[0] || 'User',
        avatar: profileData?.avatar || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
        userid: userData.user.id,
        email: userData.user.email,
        signature: profileData?.signature || '个性签名',
        title: profileData?.title || '',
        group: profileData?.group || '',
        tags: profileData?.tags || [],
        notifyCount: 0,
        unreadCount: 0,
        country: profileData?.country || 'China',
        access: profileData?.ant_design_role || 'user',
        phone: profileData?.phone || '',
        address: profileData?.address || '',
        isLogin: true,
      },
    };
  } catch (error) {
    return {
      ...handleSupabaseError(error),
      data: {
        isLogin: false,
      },
    };
  }
};

/**
 * 退出登录
 */
export const signOut = async () => {
  try {
    const { error } = await supabaseClient.auth.signOut();
    if (error) throw error;
    
    return {
      success: true,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
};

export default {
  signIn,
  getCurrentUser,
  signOut,
}; 