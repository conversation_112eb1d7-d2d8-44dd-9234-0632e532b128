import { supabaseClient, supabaseAdmin, TABLES, handleSupabaseError } from './index';

// 地址记录类型定义
export interface AddressRecord {
  id?: number;
  name: string;
  storeCodeId: string;
  address: string;
  cityName: string;
  tel?: string;
  longitude: number;
  latitude: number;
  storeCodePoiid?: string;
  marketName?: string;
  parentName?: string;
  parentStoreCodeId?: string;
  parentStoreCodePoiid?: string;
  created_at?: string;
  updated_at?: string;
}

// 分页参数类型
export interface PaginationParams {
  current?: number;
  pageSize?: number;
  name?: string;
  storeCodeId?: string;
  address?: string;
  cityName?: string;
  tel?: string;
  marketName?: string;
  parentName?: string;
  parentStoreCodeId?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 获取地址列表，支持分页和关键词搜索
 */
export async function getAddressList({ 
  current = 1, 
  pageSize = 10,
  name,
  storeCodeId,
  address,
  cityName,
  tel,
  marketName,
  parentName,
  parentStoreCodeId,
  startTime,
  endTime
}: PaginationParams = {}) {
  try {
    // 计算分页范围
    const from = (current - 1) * pageSize;
    const to = from + pageSize - 1;

    // 构建基础查询
    let query = supabaseClient
      .from(TABLES.ADDRESSES)
      .select('*', { count: 'exact' });

    // 添加搜索条件
    if (name) {
      query = query.ilike('name', `%${name}%`);
    }
    
    if (storeCodeId) {
      query = query.ilike('storeCodeId', `%${storeCodeId}%`);
    }
    
    if (address) {
      query = query.ilike('address', `%${address}%`);
    }
    
    if (cityName) {
      query = query.ilike('cityName', `%${cityName}%`);
    }
    
    if (tel) {
      query = query.ilike('tel', `%${tel}%`);
    }
    
    if (marketName) {
      query = query.ilike('marketName', `%${marketName}%`);
    }
    
    if (parentName) {
      query = query.ilike('parentName', `%${parentName}%`);
    }
    
    if (parentStoreCodeId) {
      query = query.ilike('parentStoreCodeId', `%${parentStoreCodeId}%`);
    }
    
    // 添加时间范围筛选
    if (startTime) {
      query = query.gte('updated_at', startTime);
    }
    
    if (endTime) {
      query = query.lte('updated_at', endTime);
    }

    // 执行分页查询
    const { data, error, count } = await query
      .order('updated_at', { ascending: false })
      .range(from, to);

    if (error) {
      throw error;
    }

    return {
      data,
      total: count || 0,
      success: true,
      pageSize,
      current,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 导出所有地址记录
 */
export async function exportAddresses() {
  try {
    const { data, error } = await supabaseClient
      .from(TABLES.ADDRESSES)
      .select('*')
      .order('updated_at', { ascending: false });

    if (error) {
      throw error;
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 导入地址记录
 */
export async function importAddresses(addresses: AddressRecord[]) {
  try {
    const { data, error } = await supabaseAdmin
      .from(TABLES.ADDRESSES)
      .upsert(addresses, { onConflict: 'storeCodeId' })
      .select();

    if (error) {
      throw error;
    }

    return {
      success: true,
      message: '导入成功',
      data,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 添加地址记录
 */
export async function addAddress(address: AddressRecord) {
  try {
    const { data, error } = await supabaseAdmin
      .from(TABLES.ADDRESSES)
      .insert(address)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 更新地址记录
 */
export async function updateAddress(id: number, address: Partial<AddressRecord>) {
  try {
    // 删除时间戳字段，避免冲突
    const addressToUpdate = { ...address };
    delete addressToUpdate.created_at;
    delete addressToUpdate.updated_at;
    
    // 使用Admin客户端绕过RLS策略
    // 确保Admin客户端使用服务密钥，这个密钥可以绕过所有RLS策略
    const { data, error } = await supabaseAdmin
      .from(TABLES.ADDRESSES)
      .update(addressToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('更新地址失败:', error);
      throw error;
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

/**
 * 删除地址记录
 */
export async function removeAddress(id: number) {
  try {
    const { error } = await supabaseAdmin
      .from(TABLES.ADDRESSES)
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return {
      success: true,
      data: { id },
    };
  } catch (error) {
    return handleSupabaseError(error);
  }
} 