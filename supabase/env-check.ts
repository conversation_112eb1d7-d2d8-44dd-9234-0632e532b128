import { supabaseClient } from './index';

// 创建一个函数来检查环境变量是否正确加载
export async function checkSupabaseConnection() {
  // 记录当前使用的 URL
  console.log('使用的 Supabase URL:', process.env.SUPABASE_URL);
  
  try {
    // 尝试连接到 Supabase
    const { error } = await supabaseClient.from('_test_connection').select('*').limit(1);
    
    if (error) {
      console.error('Supabase 连接测试失败:', error.message);
      return {
        success: false,
        message: `连接失败: ${error.message}`,
        envVars: {
          SUPABASE_URL: process.env.SUPABASE_URL || '未设置',
          SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY ? '已设置' : '未设置',
          SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY ? '已设置' : '未设置'
        }
      };
    }
    
    return {
      success: true,
      message: '成功连接到 Supabase',
      envVars: {
        SUPABASE_URL: process.env.SUPABASE_URL || '未设置',
        SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY ? '已设置' : '未设置',
        SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY ? '已设置' : '未设置'
      }
    };
  } catch (err: any) {
    console.error('执行连接测试时发生错误:', err);
    return {
      success: false,
      message: `执行出错: ${err.message}`,
      envVars: {
        SUPABASE_URL: process.env.SUPABASE_URL || '未设置',
        SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY ? '已设置' : '未设置',
        SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY ? '已设置' : '未设置'
      }
    };
  }
}

// 直接导出当前环境变量状态，不执行连接测试
export function getSupabaseEnvStatus() {
  return {
    SUPABASE_URL: process.env.SUPABASE_URL || 'http://172.20.52.217:8000',
    SUPABASE_ANON_KEY: Boolean(process.env.SUPABASE_ANON_KEY),
    SUPABASE_SERVICE_KEY: Boolean(process.env.SUPABASE_SERVICE_KEY)
  };
}

export default {
  checkSupabaseConnection,
  getSupabaseEnvStatus
}; 