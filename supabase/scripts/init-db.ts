import { supabaseAdmin, TABLES } from '../index';
import { mockAddresses } from './mock-data';

/**
 * 初始化Supabase数据库结构和测试数据
 */
async function initDatabase() {
  console.log('开始初始化Supabase数据库...');
  
  try {
    // 1. 创建地址表（如果不存在）
    console.log(`检查并创建${TABLES.ADDRESSES}表...`);
    
    // 使用RPC调用PostgreSQL函数创建表（这里假设你有权限创建表）
    const { error: createTableError } = await supabaseAdmin.rpc('create_address_table_if_not_exists');
    
    if (createTableError) {
      console.error('创建表失败:', createTableError);
      // 如果RPC方法不存在，则使用普通的SQL查询（备选方案）
      console.log('尝试使用SQL直接创建表...');
      
      // 这里需要Supabase的PostgreSQL直接访问权限
      // 实际项目中可能需要通过Supabase dashboard或者直接连接PostgreSQL创建表结构
      console.warn('请注意：在生产环境中，应使用数据库迁移工具管理表结构');
    }
    
    // 2. 插入测试数据
    console.log('插入测试数据...');
    const { error: insertError } = await supabaseAdmin
      .from(TABLES.ADDRESSES)
      .upsert(mockAddresses, { onConflict: 'storeCode' });
    
    if (insertError) {
      console.error('插入测试数据失败:', insertError);
    } else {
      console.log('测试数据插入成功');
    }
    
    console.log('数据库初始化完成');
  } catch (error) {
    console.error('初始化数据库时发生错误:', error);
  }
}

// 执行初始化
initDatabase()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('执行失败:', error);
    process.exit(1);
  }); 