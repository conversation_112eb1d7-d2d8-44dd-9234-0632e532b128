import { getSupabaseEnvStatus, checkSupabaseConnection } from '../env-check';

/**
 * 该脚本用于测试 Supabase 环境变量是否正确加载
 */
async function testEnvVariables() {
  console.log('========== Supabase 环境变量检查 ==========');
  
  // 检查环境变量
  const envStatus = getSupabaseEnvStatus();
  console.log('当前环境变量状态:');
  console.log(JSON.stringify(envStatus, null, 2));
  
  console.log('\n========== Supabase 连接测试 ==========');
  
  // 测试连接
  const connectionResult = await checkSupabaseConnection();
  console.log('连接测试结果:');
  console.log(JSON.stringify(connectionResult, null, 2));
  
  return connectionResult.success;
}

// 执行测试
testEnvVariables()
  .then(success => {
    console.log('\n========== 测试完成 ==========');
    if (success) {
      console.log('✅ 环境变量加载正常，Supabase 连接成功!');
      process.exit(0);
    } else {
      console.error('❌ Supabase 环境变量测试失败!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ 执行测试时发生错误:', error);
    process.exit(1);
  }); 