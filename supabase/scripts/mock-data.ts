import { AddressRecord } from '../address';

// 模拟地址数据
export const mockAddresses: AddressRecord[] = [
  {
    name: '北京分店1',
    storeCode: 'BJ101',
    address: '北京市朝阳区东路101号',
    city: '北京',
    phone: '13800138001',
    longitude: 116.4074,
    latitude: 39.9042
  },
  {
    name: '上海分店1',
    storeCode: 'SH101',
    address: '上海市黄浦区南京路101号',
    city: '上海',
    phone: '13800138002',
    longitude: 121.4737,
    latitude: 31.2304
  },
  {
    name: '广州分店1',
    storeCode: 'GZ101',
    address: '广州市天河区天河路101号',
    city: '广州',
    phone: '13800138003',
    longitude: 113.2644,
    latitude: 23.1291
  },
  {
    name: '深圳分店1',
    storeCode: 'SZ101',
    address: '深圳市南山区科技园101号',
    city: '深圳',
    phone: '13800138004',
    longitude: 114.0579,
    latitude: 22.5431
  },
  {
    name: '杭州分店1',
    storeCode: 'HZ101',
    address: '杭州市西湖区西湖路101号',
    city: '杭州',
    phone: '13800138005',
    longitude: 120.1551,
    latitude: 30.2741
  },
  {
    name: '成都分店1',
    storeCode: 'CD101',
    address: '成都市锦江区春熙路101号',
    city: '成都',
    phone: '13800138006',
    longitude: 104.0668,
    latitude: 30.6799
  },
  {
    name: '武汉分店1',
    storeCode: 'WH101',
    address: '武汉市江岸区江汉路101号',
    city: '武汉',
    phone: '13800138007',
    longitude: 114.3058,
    latitude: 30.5889
  },
  {
    name: '西安分店1',
    storeCode: 'XA101',
    address: '西安市雁塔区雁塔路101号',
    city: '西安',
    phone: '13800138008',
    longitude: 108.9402,
    latitude: 34.3416
  },
  {
    name: '南京分店1',
    storeCode: 'NJ101',
    address: '南京市鼓楼区中山路101号',
    city: '南京',
    phone: '13800138009',
    longitude: 118.7727,
    latitude: 32.0476
  },
  {
    name: '重庆分店1',
    storeCode: 'CQ101',
    address: '重庆市渝中区解放路101号',
    city: '重庆',
    phone: '13800138010',
    longitude: 106.5511,
    latitude: 29.5649
  }
]; 