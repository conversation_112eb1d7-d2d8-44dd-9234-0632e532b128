import { supabaseClient, supabaseAdmin, TABLES, handleSupabaseError } from './index';

// 用户记录类型定义
export interface UserRecord {
  id?: string;
  user_id?: string;
  name?: string;
  group?: string;
  email?: string;
  ant_design_role?: string;
  avatar?: string;
  signature?: string;
  title?: string;
  tags?: string[];
  country?: string;
  phone?: string;
  address?: string;
  created_at?: string;
  updated_at?: string;
  last_sign_in_at?: string;
}

// 查询参数类型
export interface UserQueryParams {
  pageNo?: number;
  pageSize?: number;
  name?: string;
  email?: string;
  group?: string;
  ant_design_role?: string;
}

// 分页响应类型
export interface UserListResponse {
  data: UserRecord[];
  total: number;
  success: boolean;
  pageNo: number;
  pageSize: number;
}

/**
 * 获取用户列表
 * @param params 查询参数
 */
export const getUserList = async (params: UserQueryParams): Promise<UserListResponse> => {
  try {
    const { pageNo = 1, pageSize = 20, name, group, ant_design_role } = params;
    
    // 首先获取用户资料
    let query = supabaseClient.from(TABLES.USER_PROFILES).select('*', { count: 'exact' });

    // 添加筛选条件
    if (name) {
      query = query.ilike('name', `%${name}%`);
    }
    if (group) {
      query = query.ilike('group', `%${group}%`);
    }
    if (ant_design_role) {
      query = query.eq('ant_design_role', ant_design_role);
    }

    // 分页查询
    const from = (pageNo - 1) * pageSize;
    const to = from + pageSize - 1;
    
    query = query.range(from, to).order('created_at', { ascending: false });

    const { data: profilesData, error: profilesError, count } = await query;

    if (profilesError) throw profilesError;

    console.log('Supabase用户资料数据:', profilesData);
    if (profilesData && profilesData.length > 0) {
      console.log('第一个用户资料记录:', profilesData[0]);
    }

    // 获取认证用户信息
    const { data: authUsersData, error: authUsersError } = await supabaseAdmin.auth.admin.listUsers();
    
    if (authUsersError) throw authUsersError;

    console.log('Supabase认证用户数据:', authUsersData);

    // 合并用户资料和认证信息
    const mergedData = profilesData?.map(profile => {
      const authUser = authUsersData.users.find(u => u.id === profile.user_id);
      
      const merged = {
        ...profile,
        email: authUser?.email,
        last_sign_in_at: authUser?.last_sign_in_at,
      };
      
      console.log('合并后的用户数据:', merged);
      return merged;
    }) || [];

    console.log('最终返回的合并数据:', mergedData);

    return {
      data: mergedData,
      total: count || 0,
      success: true,
      pageNo,
      pageSize,
    };
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return {
      ...handleSupabaseError(error),
      data: [],
      total: 0,
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20,
    };
  }
};

/**
 * 添加新用户
 * @param userData 用户数据
 */
export const addUser = async (userData: {
  email: string;
  password: string;
  name: string;
  group?: string;
  ant_design_role?: string;
}): Promise<any> => {
  try {
    // 使用管理员权限创建用户
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true, // 自动确认邮箱
    });

    if (authError) throw authError;

    if (!authData.user) {
      throw new Error('创建用户失败');
    }

    // 创建用户资料
    const profileData = {
      user_id: authData.user.id,
      name: userData.name,
      group: userData.group || '用户团队',
      ant_design_role: userData.ant_design_role || 'user',
      avatar: '',
      title: userData.group === '管理团队' ? '系统管理员' : '普通用户',
      tags: [], // 空数组，Supabase会自动处理
      country: 'China',
      phone: '',
      address: '',
    };

    console.log('创建用户资料，入参数据:', profileData);

    const { data: insertedProfile, error: profileError } = await supabaseAdmin
      .from(TABLES.USER_PROFILES)
      .insert(profileData)
      .select()
      .single();

    if (profileError) throw profileError;

    return {
      success: true,
      message: '用户创建成功',
      data: {
        ...insertedProfile,
        email: authData.user.email,
      },
    };
  } catch (error) {
    console.error('添加用户失败:', error);
    return handleSupabaseError(error);
  }
};

/**
 * 更新用户信息
 * @param id 用户资料ID
 * @param userData 更新数据
 */
export const updateUser = async (userId: string, userData: Partial<UserRecord>): Promise<any> => {
  try {
    console.log('updateUser函数调用，用户ID:', userId);
    console.log('updateUser函数调用，原始数据:', userData);

    // 移除不应该更新的字段
    const {
      user_id,
      created_at,
      updated_at,
      email,
      last_sign_in_at,
      id: recordId,  // 也移除id字段
      ...updateData
    } = userData;

    // 忽略这些字段，不需要更新
    void user_id;
    void created_at;
    void updated_at;
    void email;
    void last_sign_in_at;
    void recordId;

    console.log('updateUser函数调用，处理后数据:', updateData);

    // 使用管理员权限进行更新操作，使用user_id作为主键
    const { data, error } = await supabaseAdmin
      .from(TABLES.USER_PROFILES)
      .update(updateData)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Supabase更新错误:', error);
      throw error;
    }

    console.log('updateUser函数执行成功，返回数据:', data);

    return {
      success: true,
      message: '用户更新成功',
      data,
    };
  } catch (error) {
    console.error('更新用户失败:', error);
    return handleSupabaseError(error);
  }
};

/**
 * 删除用户
 * @param userId 用户认证ID（同时也是user_profiles表的主键）
 */
export const removeUser = async (userId: string): Promise<any> => {
  try {
    console.log('removeUser函数调用，用户ID:', userId);

    // 删除用户资料，使用user_id作为主键
    const { error: profileError } = await supabaseAdmin
      .from(TABLES.USER_PROFILES)
      .delete()
      .eq('user_id', userId);

    if (profileError) {
      console.error('删除用户资料失败:', profileError);
      throw profileError;
    }
    console.log('用户资料删除成功');

    // 删除认证用户
    const { error: authError } = await supabaseAdmin.auth.admin.deleteUser(userId);

    if (authError) {
      console.error('删除认证用户失败:', authError);
      throw authError;
    }
    console.log('认证用户删除成功');

    return {
      success: true,
      message: '用户删除成功',
    };
  } catch (error) {
    console.error('删除用户失败:', error);
    return handleSupabaseError(error);
  }
};

/**
 * 获取当前用户ID
 */
export const getCurrentUserId = async (): Promise<string | null> => {
  try {
    const { data: { user } } = await supabaseClient.auth.getUser();
    return user?.id || null;
  } catch (error) {
    console.error('获取当前用户ID失败:', error);
    return null;
  }
}; 