# 项目需求与实现记录

## 需求1：接入Supabase服务 (完成日期：2024-05-03)

### 用户需求
将项目接入自托管的Supabase服务，实现数据持久化存储和管理：
1. 接入部署在http://172.20.52.217:8000的Supabase服务
2. 从.env文件中读取Supabase配置参数
3. 实现地址管理模块与Supabase的集成

### 实现方案

#### Supabase客户端实现
- 创建`supabase/index.ts`作为Supabase客户端初始化和配置文件
- 支持从环境变量和备用值双重方式配置Supabase连接参数
- 提供supabaseClient和supabaseAdmin两种权限的客户端实例
- 定义统一的错误处理机制和表名常量

#### 地址管理功能实现
- 创建`supabase/address.ts`，实现地址管理相关的CRUD操作
- 提供与原mock数据API完全兼容的接口
- 添加类型定义和完善的错误处理
- 设置数据表名为`address_baidu`

#### 初始化与测试工具
- 创建`supabase/env-check.ts`用于环境变量检查
- 实现`supabase/scripts/init-db.ts`用于初始化数据库结构
- 实现`supabase/scripts/mock-data.ts`提供测试数据
- 添加`supabase/scripts/test-env.ts`用于测试环境变量加载

#### 环境变量配置
- 在`.env`文件中添加Supabase相关配置
- 通过UmiJS的define配置在编译时注入环境变量
- 实现自动读取.env文件中的所有环境变量并添加到define配置中
- 提供详细的测试和诊断工具

#### 技术实现要点
- 使用`@supabase/supabase-js`客户端库连接Supabase服务
- 实现环境变量读取的容错机制
- 提供统一的错误处理和响应格式
- 与原有的地址管理模块保持API一致性

---

## 需求2：Supabase用户登录与认证 (完成日期：2024-05-15)

### 用户需求
实现基于Supabase的真实用户登录认证系统，替代之前的mock数据模拟登录：
1. 使用Supabase Authentication服务进行用户认证
2. 利用已创建的用户资料表`user_profiles`获取用户角色信息
3. 仅支持邮箱密码登录方式
4. 实现登录、登出和获取当前用户信息功能

### 实现方案

#### Supabase认证服务实现
- 创建`supabase/auth.ts`作为认证服务的核心实现
- 实现`signIn`函数，处理用户登录并获取角色信息
- 实现`getCurrentUser`函数，获取当前登录用户详细信息
- 实现`signOut`函数，处理用户退出登录

#### API服务整合
- 更新`src/services/ant-design-pro/api.ts`，重定向API调用到Supabase服务
- 修改`login`、`currentUser`和`outLogin`函数，使用Supabase认证API
- 保留原有API接口结构，确保与系统其他部分兼容

#### 登录页面优化
- 简化`src/pages/User/Login/index.tsx`，移除不必要的登录方式
- 更新登录表单，添加邮箱验证规则
- 优化错误处理和提示信息展示
- 保留自动登录功能支持

#### 全局状态与路由控制
- 更新`src/app.tsx`中的用户状态管理
- 优化`getInitialState`函数，适配Supabase认证状态
- 修改获取用户信息逻辑，确保正确处理登录状态
- 保持原有的路由权限控制

#### 数据模型与类型定义
- 扩展`LoginResult`类型，添加必要的字段
- 更新用户信息结构，确保与Supabase用户模型一致
- 优化错误处理和类型安全

#### 技术实现要点
- 使用Supabase Auth SDK的`signInWithPassword`进行安全登录
- 使用`getSession`和`getUser`获取当前用户状态
- 结合Supabase数据库查询和认证服务获取完整用户信息
- 保持与原有系统的兼容性，实现平滑迁移
- 完善的错误处理和用户体验优化

---

## 需求3：地址管理页导出功能优化（完成日期：2024-06-07）

### 用户需求
1. 保持现有导出的excel不变，导出时再新增一个excel，也就是每次导出会导出两个excel表格。
2. 新增的excel表格对应的列信息如下（supabase字段名 -> excel的列名）：
   - marketName -> 市场
   - storeCodeId -> 子点cpid(门店id)
   - storeCodePoiid -> 子点poiid
   - cityName -> 城市
   - name -> 子点名称
   - address -> 子点地址
   - longitude -> 子点x
   - latitude -> 子点y
   - parentStoreCodePoiid -> 主点poiid
   - parentName -> 主点名称
   - 已上线（固定值）-> 备注
   - parentStoreCodeId -> 母店号
3. 新增导出的excel名字为：kfc_address_for_tencent_YYYY_MM_DD_HH_MM_SS.xlsx

### 实现方案
- 在handleExport函数中，保持原有导出excel逻辑不变。
- 新增一份excel导出，字段和表头按上述需求，备注列固定为"已上线"。
- 用xlsx库生成并下载第二个excel，文件名按要求拼接时间戳。
- 用户每次点击导出时，会同时下载两份excel文件：
  1. 原有百度格式（kfc_address_for_baidu_YYYY_MM_DD_HH_MM_SS.xlsx）
  2. 新增腾讯格式（kfc_address_for_tencent_YYYY_MM_DD_HH_MM_SS.xlsx）

### 技术实现要点
- 字段映射和顺序严格按照需求实现。
- 备注列为固定值"已上线"。
- 文件名自动带时间戳，防止重复。
- 兼容原有导出全部和导出选中功能。

---

## 需求4：地址管理页面 (完成日期：当前)

### 用户需求
用户要求增加一个地址信息管理页面，主要功能如下：
1. 展示地址信息列表，列有：id、名称、storeCode、地址、城市、电话、经度、纬度
2. 支持分页功能
3. 顶部有导入导出功能，支持Excel格式
4. 支持搜索查询功能，可搜索名称、storeCode、地址
5. 使用mock数据提供25条示例数据

### 实现方案

#### 数据服务实现
- 创建`src/services/ant-design-pro/address.ts`，定义API接口
- 创建`mock/address.ts`，实现25条地址数据的模拟服务
- 实现了获取列表、添加、更新、删除、导入、导出API
- 为每条记录生成按城市首字母+序号的storeCode

#### 页面组件实现
- 创建`src/pages/Address/index.tsx`作为主页面
- 使用ProTable组件实现表格展示和分页
- 实现顶部的新建、导入、导出功能
- 实现搜索功能，支持按名称、storeCode和地址搜索
- 表格中展示storeCode列，位于名称列之后

#### 表单组件实现
- 创建`src/pages/Address/components/CreateForm.tsx`用于新增地址
- 创建`src/pages/Address/components/UpdateForm.tsx`用于编辑地址
- 表单中添加storeCode输入字段

#### 其他配置
- 在`config/routes.ts`中添加地址管理页面路由
- 在国际化文件中添加相应的菜单翻译
  - 中文：`menu.address: 地址管理`
  - 英文：`menu.address: Address Management`

#### 技术实现要点
- 使用React.lazy动态导入表单组件
- 使用ProTable实现高效的表格展示和搜索
- 使用mock服务模拟API接口
- 动态生成地址数据，包含基于城市的经纬度生成
- 模拟数据中使用城市首字母+序号生成storeCode

---

## 需求5：优化地址管理功能 (完成日期：当前)

### 用户需求
1. 实现Excel导入功能，按照指定的列名映射关系处理数据
2. 控制地址列表默认显示的列
3. 添加更新时间列
4. 添加时间范围搜索功能

### 实现方案

#### Excel导入功能实现
- 安装`xlsx`库用于解析Excel文件
- 更新`AddressRecord`接口，添加新字段：storeCodePoiid、marketName、parentName、parentStoreCodeId、parentStoreCodePoiid
- 实现Excel文件的读取和解析功能
- 按照指定映射关系处理Excel数据：
  - '子点cpid(门店id)' -> 'storeCodeId'
  - '子点名称' -> 'name'
  - '子点地址' -> 'address'
  - '城市' -> 'cityName'
  - '子点x' -> 'longitude'
  - '子点y' -> 'latitude'
  - '子点poiid' -> 'storeCodePoiid'
  - '市场' -> 'marketName'
  - '主点名称' -> 'parentName'
  - '母店号' -> 'parentStoreCodeId'
  - '主点poiid' -> 'parentStoreCodePoiid'
- 限制Excel文件大小为3MB

#### 列表显示控制
- 使用ProTable的`columnsState`属性设置默认列显示状态
- 默认只显示：门店id、名称、地址、城市、电话、经度、纬度、更新时间和操作列
- 更新CreateForm和Editor组件，添加对应的新字段输入项

#### 更新时间显示与搜索
- 添加更新时间列，使用`updated_at`字段
- 设置`valueType: 'dateTime'`以日期时间格式显示
- 添加时间范围搜索功能：
  - 添加时间范围选择器，使用`valueType: 'dateTimeRange'`
  - 更新`PaginationParams`接口，添加起始时间和结束时间参数
  - 使用Supabase的`gte`和`lte`操作符实现时间范围筛选

#### 技术实现要点
- 使用`xlsx`库实现Excel文件的读取和解析
- 通过类型映射确保数据类型转换正确
- 使用ProTable的高级特性控制列显示
- 实现时间范围的筛选功能
- 优化用户体验，提供明确的错误提示

---

## 需求6：优化地址电话信息获取 (完成日期：当前)

### 用户需求
1. 实现从KFC API自动获取电话信息功能
2. 优化批量处理，支持大量地址数据的情况
3. 在添加和编辑地址时自动补充电话信息

### 实现方案

#### API工具函数封装
- 创建`src/utils/kfcApi.ts`文件，独立封装KFC API相关功能
- 实现`batchFetchTelFromKFC`函数进行批量获取电话信息
- 实现`fetchTelFromKFC`函数进行单个门店电话信息获取
- 添加请求签名逻辑，使用MD5进行签名计算

#### 批量处理优化
- 实现分批处理逻辑，每批最多处理100个storeCode
- 添加批次间延迟机制，防止频繁请求导致API限流
- 优化错误处理，记录详细的批次处理状态
- 使用Promise.all处理并发请求，提高处理效率

#### 导入功能增强
- 修改Excel导入流程，支持从KFC API获取电话信息
- 先收集所有storeCode，然后批量获取电话信息
- 将获取到的电话信息添加到对应的地址对象中

#### 表单功能增强
- 在新增地址时，自动从API获取电话信息
- 在编辑地址时，如果用户未手动填写电话，则自动获取
- 优先保留用户手动输入的电话信息

#### 技术实现要点
- 使用`crypto-js`库进行MD5签名计算
- 使用批处理逻辑解决请求URI过长问题
- 通过模块化设计提高代码复用性
- 实现延迟函数优化请求频率
- 通过条件判断避免不必要的API请求

---

## 需求7：地址管理多选和批量操作功能 (完成日期：当前)

### 用户需求
1. 增加表格行选择功能，可选中多行数据
2. 选中行后显示批量操作按钮：删除选中、导出选中、取消选择
3. 优化导出功能，支持导出选中记录，文件名包含时间戳
4. 优化Excel表头映射，使导出数据更清晰易读

### 实现方案

#### 多选功能实现
- 添加行选择状态管理：`selectedRowKeys`和`selectedRows`
- 配置ProTable的`rowSelection`属性启用行选择功能
- 实现选中状态UI展示，包括选中项数量统计
- 添加选中行操作区域显示逻辑，当有选中行时才显示

#### 批量操作按钮实现
- 创建`TableAlertRender`组件，显示操作按钮和选中数量
- 实现"删除选中"按钮（红色），增加二次确认
- 实现"导出选中"按钮（蓝色），导出选中的数据行
- 实现"取消选择"按钮，清空选中状态

#### 导出功能优化
- 重构`handleExport`函数，支持全部导出和选中导出两种模式
- 优化Excel列名映射，使用更清晰的字段名称：
  - 'storeCodeId' -> 'ID(必填)'
  - 'name' -> '名称(必填)'
  - 'address' -> '地址(必填)'
  - 'cityName' -> '城市(必填)'
  - 'tel' -> '电话(非必填)'
  - 'longitude' -> '经度(非必填)'
  - 'latitude' -> '纬度(非必填)'
  - 'marketName' -> '市场(非必填)'
- 导出文件名添加时间戳：`kfc_address_for_baidu_YYYY_MM_DD_HH_MM_SS.xlsx`
- 调整Excel列宽，使内容显示更合理

#### 数据操作优化
- 修改Supabase数据查询，按更新时间降序排序，最新记录显示在前面
- 优化地址更新功能，确保不提交created_at和updated_at字段
- 使用复制和删除字段的方式处理更新数据
- 更新表单组件，确保提交的数据中不包含时间戳字段

#### 技术实现要点
- 使用React状态管理选中行数据
- 利用ProTable的表格提醒功能展示批量操作UI
- 通过条件渲染控制操作按钮显示
- 使用批量Promise处理实现并发删除
- 优化数据处理流程，避免不必要的字段更新
- 使用日期格式化生成带时间戳的文件名

---

## 需求8：优化地址管理导入导出功能 (完成日期：当前)

### 用户需求
1. 优化Excel导入功能，解决大数据量导入时的问题
2. 改进导出功能，确保导出的是当前查询条件下的数据，而非总是整个表的数据

### 实现方案

#### Excel导入优化
- 实现数据批量处理机制，每100条记录同步一次到Supabase
- 添加进度提示，在每批次导入后显示当前导入进度
- 完善错误处理，支持单批次失败时继续处理其他批次
- 最终显示总导入情况，包括成功数量和总数量

#### 导出功能改进
- 修改"导出"按钮行为，使其导出当前查询条件下的数据
- 通过维护当前查询参数的状态实现高效数据导出
- 导出时使用当前筛选条件但设置大pageSize以获取全部匹配数据
- 保留原有的"导出选中"功能，维持一致的用户体验

#### 技术实现要点
- 使用数组分割方法`slice`实现数据批量处理
- 使用状态管理记录当前查询参数
- 在表格的`request`处理函数中保存当前请求参数
- 利用异步处理和Promise处理数据同步
- 完善的用户界面反馈，提供清晰的导入进度指示

---

## 需求9：地址管理页省市区信息自动添加功能 (完成日期：当前)

### 用户需求
1. 在导入Excel时，自动在地址前添加省市区信息
2. 省市区信息从KFC API（emap/getStoreByCode接口）获取
3. 从API返回的数据中取值：省->marketName、市->cityName、区->districtName

### 实现方案

#### KFC API工具增强
- 重构`src/utils/kfcApi.ts`文件，扩展现有功能
- 创建新的`batchFetchStoreInfoFromKFC`函数，获取完整的门店信息（包括电话和省市区）
- 定义`StoreInfo`接口，包含tel、marketName、cityName、districtName字段
- 保持向后兼容性，通过旧函数调用新函数实现

#### Excel导入功能优化
- 修改Excel导入流程，获取完整的门店信息
- 将省市区信息自动添加到地址字段前面
- 根据API返回数据更新城市和市场信息
- 保留原始地址信息，只在前面追加省市区
- 地址格式为：`省市区+原地址`

#### 新增和更新功能优化
- 修改`handleAdd`和`handleUpdate`函数
- 当用户添加或更新地址时，自动获取并添加省市区信息
- 优先尊重用户手动输入的省市区信息
- 只在用户未输入相关信息时才自动填充

#### 技术实现要点
- 使用API返回数据中的marketName作为省份信息
- 使用API返回数据中的cityName作为城市信息
- 使用API返回数据中的districtName作为区县信息
- 对地址字段进行智能拼接，避免重复信息
- 保持原有的批量处理和错误处理机制
- 处理缺失数据场景，确保功能健壮性

---

## 需求10：优化KFC API门店ID处理 (完成日期：当前)

### 用户需求
1. 处理门店ID中包含后缀的情况，例如`YZN002_D1`格式
2. 在调用KFC API（emap/getStoreByCode）时，需要去掉门店ID中的`_xx`后缀
3. 确保省市名称相同时（如直辖市）不重复显示

### 实现方案

#### 门店ID清理功能实现
- 创建`cleanStoreCode`函数，专门处理门店ID格式
- 使用字符串操作识别并移除`_`及其后面的所有字符
- 例如：将`YZN002_D1`转换为`YZN002`

#### KFC API调用优化
- 重构`batchFetchStoreInfoFromKFC`函数，实现更智能的门店ID处理
- 创建原始门店ID到清理后门店ID的映射关系
- 创建清理后门店ID到原始门店ID列表的反向映射
- 使用去重后的清理后门店ID列表进行API调用，减少请求次数
- 将API返回的结果关联到所有对应的原始门店ID上

#### 省市区地址拼接优化
- 修改地址拼接逻辑，处理直辖市（北京、上海、天津、重庆）等特殊情况
- 当省名与市名相同时，只保留市名，避免出现重复
- 在Excel导入、新增地址和更新地址功能中统一实现此逻辑

#### 技术实现要点
- 使用字符串`indexOf`方法快速定位下划线位置
- 使用`substring`截取下划线前的内容作为清理后的门店ID
- 利用Map数据结构建立双向映射关系，提高查找效率
- 应用Set进行数据去重，减少不必要的API请求
- 优化代码结构，增强可读性和可维护性
- 保持API处理的一致性，确保各类场景下门店信息获取的准确性

---

## 需求11：优化KFC API调用批次大小 (完成日期：当前)

### 用户需求
1. 将API调用（emap/getStoreByCode）的批次大小从100条减少到30条
2. 同步修改Excel导入数据的批处理大小

### 实现方案

#### API调用批次优化
- 修改`src/utils/kfcApi.ts`中的`batchFetchStoreInfoFromKFC`函数
- 将批次大小`batchSize`从100调整为30
- 保持原有的批量处理逻辑和错误处理机制不变

#### 数据导入批次同步调整
- 修改`src/pages/Address/index.tsx`中Excel导入功能的批处理大小
- 将导入Supabase时的批次大小也从100调整为30
- 确保数据导入进度提示正确反映新的批次大小

#### 技术实现要点
- 通过减小批次大小，降低单次API请求的数据量
- 改善请求响应时间，提高系统稳定性
- 更好地匹配API服务器的处理能力
- 保持现有功能的一致性和可靠性

---

## 需求12：优化KFC API调用重试机制 (完成日期：当前)

### 用户需求
1. 针对KFC API调用时未能查询到的门店信息，添加重试机制
2. 记录未在API响应中返回信息的门店ID
3. 对这些未查询到的门店ID重新发起API请求
4. 最多重试3次，若3次后仍未查到，则放弃
5. 重试机制仅在Excel批量导入时启用，新增和编辑单个地址时不启用

### 实现方案

#### API调用重试机制设计
- 重构KFC API调用逻辑，将API请求抽取为独立函数`fetchStoreInfoFromAPI`
- 添加`enableRetry`参数控制是否启用重试机制，默认为false
- 实现门店信息查询结果的精确跟踪，返回"已找到"和"未找到"两部分结果
- 使用Set数据结构高效记录和跟踪未找到的门店ID
- 针对所有未找到的门店ID实现最多3次的重试查询

#### 重试流程优化
- 首次查询后，收集所有未找到的门店ID
- 仅在Excel导入功能中启用重试机制，传入`enableRetry=true`
- 对未找到的门店ID进行分批处理，每批最多30个
- 每次重试后，更新"仍未找到"的门店ID列表
- 添加重试间隔，避免频繁请求
- 每次重试时提供日志，记录重试进度和结果

#### 技术实现要点
- 使用函数式编程思想，拆分API调用和结果处理逻辑
- 通过参数控制重试机制的启用，保持灵活性
- 采用异步处理和Promise处理重试机制
- 实现精确的门店ID映射，确保原始ID与清理后ID之间的对应关系
- 优化日志输出，方便问题排查
- 保持向后兼容性，确保现有功能不受影响

---

## 需求13：地址管理页按钮状态优化 (完成日期：当前)

### 用户需求
在地址管理页面中，当选中某些门店时，需要置灰外层的主"导入"、"导出"按钮，防止用户误点，避免操作冲突。

### 实现方案

#### 按钮状态控制
- 修改`toolBarRender`中的"导入"和"导出"按钮
- 添加`disabled`属性，基于`selectedRowKeys.length > 0`判断是否禁用按钮
- 当有选中行时，主要的"导入"和"导出"按钮变为置灰状态
- 保持"新建"按钮正常可用，不受选中状态影响

#### 用户体验优化
- Upload组件同时设置`disabled`属性，确保整个导入功能被禁用
- 按钮内部的Button组件也设置`disabled`属性，保证视觉一致性
- 用户在有选中行时只能使用"导出选中"功能，避免操作混淆
- 通过"取消选择"可以重新启用主要的导入导出功能

#### 技术实现要点
- 利用React状态`selectedRowKeys`的长度判断是否有选中项
- 同时控制Upload组件和内部Button的禁用状态
- 保持原有功能逻辑不变，仅添加状态控制
- 确保按钮状态与选中状态实时同步

---

## 需求14：地址管理页腾讯信息同步接口 (完成日期：当前)

### 用户需求
在地址管理页增加腾讯信息同步接口，具体要求如下：
1. 在用户点击导入按钮时，异步调用腾讯API将门店信息同步给腾讯服务
2. 调用时机与KFC API调用类似，在Excel导入过程中执行
3. 使用`https://locate.yumchina.com/dt/kfc/restapi/lbsproxy`接口同步门店信息
4. 支持批量同步，提供进度反馈

### 实现方案

#### 腾讯API工具实现
- 创建`src/utils/tencentApi.ts`文件，专门处理腾讯地图API相关功能
- 实现`syncStoreToTencent`函数进行单个门店信息同步
- 实现`batchSyncStoresToTencent`函数进行批量门店信息同步
- 实现`generateUniqueId`函数生成唯一标识符

#### API请求参数设计
- 接口URL：`https://locate.yumchina.com/dt/kfc/restapi/lbsproxy`
- 请求参数结构：
  - `mapKind`: '2'
  - `reqKind`: '1'
  - `reqMethod`: '/ws/native/lbsserver/api/lbsdata/create'
  - `reqParam`: JSON字符串化的数据对象

#### reqParam数据结构
- `dataid`: 随机生成的唯一标识符（时间戳+随机字符串）
- `source`: 'kfc_chesuqu_offline'
- `type`: 1
- `status`: 2
- `data`对象包含：
  - `gas_data`: 'my test data'
  - `name`: 取Excel'子点名称'列内容
  - `lng`: 取Excel'子点x'列内容（经度）
  - `lat`: 取Excel'子点y'列内容（纬度）
  - `addr`: 取Excel'子点地址'列内容

#### Excel导入流程集成
- 在Excel导入功能中集成腾讯同步
- 在Supabase数据导入完成后，自动执行腾讯同步
- 将地址数据转换为腾讯API所需的格式
- 支持同步进度显示，实时反馈同步状态
- 在最终成功提示中显示导入和同步的统计信息

#### 技术实现要点
- 使用TypeScript接口定义数据结构，确保类型安全
- 实现异步批量处理，避免同时发送过多请求
- 添加适当的延迟机制，防止API限流
- 提供详细的错误处理和日志记录
- 支持进度回调，提升用户体验
- 与现有Excel导入流程无缝集成
- 确保数据格式转换的准确性
- **使用FormData格式发送请求**，而非JSON格式
- 移除Content-Type头，让浏览器自动设置multipart/form-data边界
- **实现批量同步机制**，类似KFC API的分批处理方式
- 将data参数从单个对象改为数组，支持一次提交多个门店信息
- 使用30个门店为一批的批处理大小，提高同步效率

#### 批量同步优化 (完成日期：当前)

##### 优化背景
原来的实现是逐个门店调用腾讯API，效率较低。参考KFC API的分批处理方式，优化为批量同步模式。

##### 数据结构调整
- **原来的data结构**：`{ gas_data: '', name: '', lng: 0, lat: 0, addr: '' }`
- **现在的data结构**：`[{ gas_data: '', name: '', lng: 0, lat: 0, addr: '' }, ...]`

##### 批量处理逻辑
- 每批最多处理30个门店，与KFC API保持一致
- 分批发送到腾讯API，减少请求次数
- 批次间添加500ms延迟，避免API限流
- 提供详细的批次进度反馈

---

## 需求15：地址管理页面上传进度条优化 (完成日期：当前)

### 用户需求
在地址管理页面上传Excel文件时，优化用户体验，要求如下：
1. 将当前使用toast展示的上传进度改为精美的进度条效果
2. 分别展示两个主要操作的进度：
   - 进度条1：Excel数据录入操作进度（包含KFC API emap/getStoreByCode接口处理）
   - 进度条2：腾讯服务同步操作进度（包含腾讯API restapi/lbsproxy接口处理）
3. 要求进度条效果精美，提供更直观的进度展示

### 实现方案

#### 精美进度条组件设计
- 创建`src/pages/Address/components/UploadProgress.tsx`进度条组件
- 使用Ant Design的Modal、Progress、Space等组件构建精美UI
- 设计两个独立的进度条：数据录入进度和腾讯同步进度
- 采用渐变色进度条：数据录入使用蓝绿渐变，腾讯同步使用紫绿渐变
- 添加状态图标：正在进行、成功完成、异常错误等不同状态的视觉反馈

#### 进度条功能特性
- **整体状态管理**：uploading、importing、syncing、completed、error
- **详细进度信息**：实时显示当前进度数字和总数
- **步骤说明文本**：清晰显示当前操作内容和批次信息
- **完成状态展示**：操作完成后显示最终统计信息
- **交互控制**：只有在完成或错误状态时才能关闭对话框

#### 数据录入进度条实现
- 涵盖Excel文件读取解析过程
- KFC API调用占进度的50%，实时显示批次处理信息
- 数据处理和省市区信息添加占10%
- Supabase数据库导入占40%，实时显示导入数量
- 支持批次处理进度回调，精确跟踪每个批次的处理状态

#### 腾讯同步进度条实现
- 专门展示腾讯API同步进度
- 实时显示同步批次信息和完成数量
- 支持批量同步的进度回调机制
- 提供详细的同步状态和错误处理

#### KFC API工具增强
- 为`batchFetchStoreInfoFromKFC`函数添加进度回调参数
- 在批处理循环中调用进度回调，提供实时的批次处理信息
- 进度回调包含：已完成数量、总数量、当前批次信息
- 保持向后兼容性，进度回调为可选参数

#### 上传流程重构
- 完全重写`handleImport`函数，移除所有toast提示
- 使用状态管理替代loading提示，提供更丰富的进度信息
- 分阶段更新进度条状态：文件读取→数据录入→腾讯同步→完成
- 添加详细的错误处理，确保异常情况下的用户体验
- 优化进度计算逻辑，确保进度条平滑准确

#### 技术实现要点
- 使用TypeScript严格类型定义，确保状态管理的类型安全
- 采用React Hooks进行状态管理，支持复杂的进度状态更新
- 实现异步进度回调机制，支持批量处理的实时进度更新
- 使用Ant Design组件库构建专业级UI效果
- 优化进度计算算法，确保进度条反映真实的处理进度
- 实现响应式设计，适配不同屏幕尺寸
- 添加动画效果和过渡，提升用户体验

---

## 需求18：AI点餐反馈统计页面 (完成日期：当前)

### 用户需求
创建一个新的"AI点餐反馈统计"页面，与现有的管理页、地址管理同级，具体要求如下：
1. 这是一个APP功能反馈统计页面，用于列出用户返回的问题
2. 列表的列包含字段：id、手机号、问题类别、反馈内容、图片列表、视频、反馈时间
3. 支持通过手机号或时间范围(起始时间和结束时间)来搜索
4. 数据接口为：`https://tappagent.kfc.com.cn/agent-service/aiagent/feedback/query`
5. 接口需要特殊的签名认证机制

### 实现方案

#### API服务实现
- 创建`src/services/ant-design-pro/feedback.ts`服务文件
- 实现特殊的请求头签名加密逻辑：
  - 使用固定的`client_key`和`client_sec`进行签名
  - 生成时间戳`kbcts`和MD5签名`kbsv`
  - 构建签名字符串格式：`${client_key}\t${client_sec}\t${kbcts}\t${path}\t${queryString}`
- 使用`crypto-js`库进行MD5哈希计算
- 定义完整的TypeScript类型定义

#### 页面组件实现
- 创建`src/pages/AiFeedback/index.tsx`主页面组件
- 使用ProTable组件实现表格展示、搜索和分页功能
- 实现图片预览功能，支持多图展示和点击预览
- 实现视频链接展示，点击可新窗口打开
- 时间戳格式化显示，使用中文本地化格式

#### 搜索功能实现
- 手机号精确搜索功能
- 时间范围搜索，使用`dateTimeRange`组件选择起始和结束时间
- 搜索参数自动转换为API所需格式
- 支持搜索重置和条件组合查询

#### 路由和菜单配置
- 在`config/routes.ts`中添加新路由：`/ai-feedback`
- 配置菜单图标为`messageOutlined`，与其他管理页面同级
- 添加中英文菜单翻译：
  - 中文：`menu.aiFeedback: AI点餐反馈统计`
  - 英文：`menu.aiFeedback: AI Ordering Feedback Statistics`

#### 技术实现要点
- 使用ProTable的高级搜索和分页功能
- 实现自定义的API签名机制，确保请求安全
- 图片列表的JSON解析和安全处理
- 视频URL的有效性检查和外部链接跳转
- 完善的错误处理和用户提示
- 响应式表格设计，支持横向滚动
- TypeScript严格类型检查，确保代码质量

#### 数据展示优化
- 反馈内容支持超长文本省略显示
- 问题类别使用Tag组件高亮显示
- 图片支持缩略图和预览功能
- 时间显示本地化格式，便于用户阅读
- 表格列宽优化，确保内容完整显示

#### API调用方式调整 (完成日期：当前)
- **问题发现**：接口实际需要使用POST方式请求，而不是GET方式
- **解决方案**：
  - 修改`queryFeedback`函数，使用POST请求方式
  - 将查询参数从URL查询字符串改为POST请求体
  - 相应调整签名逻辑，POST请求的签名字符串包含JSON序列化的参数
  - 添加正确的Content-Type头：`application/json`

#### URL参数污染问题修复 (完成日期：当前)
- **问题发现**：请求URL被自动添加了`?token = 123`参数，影响API调用
- **根本原因**：`src/requestErrorConfig.ts`中的请求拦截器会给所有请求自动添加token参数
- **解决方案**：
  - 修改请求拦截器，为AI反馈API添加例外处理
  - 当URL包含`/agent-service/aiagent/feedback/query`时，跳过token参数添加
  - 确保反馈API使用自己的签名机制，不受全局token影响

#### API必需参数补充 (完成日期：当前)
- **问题发现**：通过对比失败和成功的curl请求，发现缺少必需的API参数
- **缺少的参数**：business、brand、agentService、portalSource、deviceId
- **解决方案**：
  - 在请求参数中添加固定的必需参数：
    - `business: 'preorder'`
    - `brand: 'KFC_PRE'`
    - `agentService: 'ali'`
    - `portalSource: 'aiordering'`
    - `deviceId: '51055aac3d225643d7d314269adae023'`
  - 这些参数会影响签名计算，确保API调用的成功

#### 签名路径问题修复 (完成日期：当前)
- **问题发现**：签名计算中使用的path不正确，包含了完整路径而非签名所需的相对路径
- **错误用法**：使用了`/agent-service/aiagent/feedback/query`作为签名path
- **正确用法**：签名应该使用`/aiagent/feedback/query`
- **解决方案**：
  - 分离签名路径和完整API路径
  - `signPath`: `/aiagent/feedback/query` (用于签名计算)
  - `fullPath`: `/agent-service/aiagent/feedback/query` (用于实际请求)
  - 这是签名验证失败的根本原因

#### 页面UI和功能优化 (完成日期：当前)

##### 表格列调整和排序优化
- **"反馈时间"列位置调整**：将反馈时间列移到表格最前面，方便用户快速查看
- **默认排序规则**：设置反馈时间列默认降序排序，最新的反馈显示在前面
- **排序功能完善**：为反馈时间列添加sorter功能，支持用户手动排序

##### 环境切换功能
- **双环境支持**：
  - 生产环境：`https://aiordering.kfc.com.cn/agent-service/aiagent/feedback/query`
  - 测试环境：`https://tappagent.kfc.com.cn/agent-service/aiagent/feedback/query`
- **UI组件设计**：
  - 在表格toolbar区域添加环境切换开关
  - 使用Switch组件，标注"生产"/"测试"状态
  - 使用不同颜色文字标识当前环境（绿色=生产，橙色=测试）
- **默认配置**：默认启用生产环境，确保用户正常使用生产数据
- **API服务支持**：修改`queryFeedback`函数，支持`isProduction`参数动态切换baseUrl
- **自动刷新优化**：环境切换时自动调用`actionRef.current?.reload()`刷新数据，无需手动点击刷新按钮

##### 搜索按钮布局优化 (完成日期：当前)
- **按钮顺序调整**：查询按钮放在前面，重置按钮放在后面
- **按钮样式优化**：重置按钮使用`danger`属性，显示为红色风格
- **自定义渲染**：使用`optionRender`自定义搜索表单按钮布局和样式
- **重置功能修复**：重置按钮逻辑调整为`resetFields() + submit()`，确保重置后自动刷新数据

##### 反馈内容列优化 (完成日期：当前)
- **显示优化**：
  - 增加列宽从250px到300px，显示更多内容
  - 调整内容区域最大宽度到280px
  - 设置`wordBreak: 'break-all'`防止长单词溢出
  - 优化行高`lineHeight: '1.4'`提升可读性
- **交互优化**：
  - 添加Tooltip悬停显示完整内容
  - 添加`copyable: true`属性实现一键复制功能
  - 设置鼠标悬停指针样式提示可点击
- **用户体验**：hover查看完整内容，点击复制到剪贴板

---

## 技术问题记录

### TypeScript类型声明冲突问题 (解决日期：当前)

#### 问题描述
在`src/pages/Address/components/typings.d.ts`中使用`declare module './Editor'`声明模块并导出默认组件时，与`src/pages/Address/components/Editor.tsx`中的默认导出发生冲突，导致TypeScript报错：
```
标识符"Editor"重复。ts(2300)
```

#### 原因分析
1. TypeScript 声明文件 (.d.ts) 和源文件 (.tsx) 虽然扩展名不同，但它们都属于相同的模块解析系统
2. 当在 typings.d.ts 中使用 `declare module './Editor'` 时，实际是为相对路径 `./Editor` 声明类型，与Editor.tsx文件冲突
3. 两个文件中都有 `export default Editor` 的声明，导致同一模块路径下有重复的默认导出

#### 解决方案
1. 避免在 typings.d.ts 中使用 `declare module './xxx'` 为已存在的同名 .tsx 文件声明类型
2. 改为直接导出接口，如 `export interface EditorProps {...}`
3. 在需要使用接口的文件中通过 `import { EditorProps } from './typings'` 导入

#### 最佳实践
- 声明文件(.d.ts)主要用于为没有类型定义的第三方库添加类型，或声明全局类型
- 对于自己实现的组件，应直接在组件文件中定义类型，或将类型定义导出到单独的types文件中
- 如果需要在多个组件间共享类型，应使用常规的导出/导入模式，而非模块声明

### 环境变量加载问题 (解决日期：2024-05-03)

#### 问题描述
项目中创建了.env文件并添加了Supabase相关环境变量，但在前端应用中无法正确获取这些环境变量，导致Supabase客户端连接失败。

#### 原因分析
1. 前端应用中的`process.env`变量与Node.js中的不同，不会自动读取.env文件
2. 在基于UmiJS的前端项目中，需要通过专门的配置将环境变量注入到前端代码中
3. 默认情况下，.env中的变量仅在Node.js构建环境中可用，不会自动传递到浏览器环境

#### 解决方案
1. 使用UmiJS的define配置在编译时将环境变量注入到前端代码中
2. 在`config/config.ts`中添加dotenv配置，自动读取.env文件
3. 实现智能的环境变量自动注入机制：
   ```typescript
   // 自动添加.env中的所有环境变量到define配置
   const defineEnvVars: Record<string, any> = {};
   if (envResult.parsed) {
     Object.keys(envResult.parsed).forEach((key) => {
       defineEnvVars[`process.env.${key}`] = process.env[key];
     });
   }
   ```
4. 提供备用策略，确保即使.env文件不存在也能正常工作

#### 最佳实践
- 使用UmiJS的define配置在编译时注入环境变量，而非依赖运行时读取
- 实现自动化机制读取并注入.env中的所有变量，避免每添加新变量都要修改配置
- 为关键配置提供备用值，确保应用在环境变量缺失时依然可用
- 添加环境变量加载状态的日志输出，方便调试
- 区分开发环境和生产环境的环境变量处理策略

---

## 需求16：地址管理功能拆分 (完成日期：当前)

### 用户需求
1. 将原地址管理页面拆分为两个独立页面：
   - "地址管理"页：负责门店信息的导入导出和维护，使用address_baidu表
   - "地址管理(腾讯)"页：负责同步门店信息到腾讯服务，使用address_tencent表
2. 两个页面使用各自独立的数据表，功能实现各自独立
3. "地址管理"页面移除同步到腾讯服务的功能代码
4. "地址管理(腾讯)"页面只保留kfc_address_for_tencent_YYYY_MM_DD_HH_MM_SS.xlsx格式的导出

### 实现方案

#### 腾讯地址数据服务
- 创建新的`address_tencent.ts`文件，定义与腾讯地址相关的数据服务
- 在Supabase中定义新的表名常量：`TENCENT_ADDRESSES: 'address_tencent'`
- 实现完整的CRUD接口，与原地址管理保持一致的API风格

#### 腾讯地址管理页面
- 创建新的页面组件`src/pages/AddressTencent/index.tsx`
- 实现与原地址管理页面类似的UI和功能，但移除了腾讯同步部分
- 实现专门用于腾讯格式的导出功能，生成kfc_address_for_tencent格式Excel

#### 路由和菜单配置
- 在`config/routes.ts`添加新的路由配置，指向腾讯地址管理页面
- 在国际化配置中添加菜单名称翻译：
  - 中文：`menu.address.tencent: 地址管理(腾讯)`
  - 英文：`menu.address.tencent: Address Management (Tencent)`

#### 原地址管理页面优化
- 修改原`handleImport`函数，移除腾讯同步相关代码
- 保留原有的两种格式导出功能不变
- 添加提示文本，告知用户腾讯同步功能已移至专门的页面

#### 组件复用与维护
- 为腾讯地址管理页面复制和调整核心组件：
  - `CreateForm`：创建新地址表单
  - `Editor`：编辑地址表单
  - `UploadProgress`：简化后的上传进度组件

#### 技术实现要点
- 通过复制和调整代码，确保两个页面功能相互独立
- 两个页面使用各自的数据表，完全隔离数据操作
- 保持组件和API接口命名规范统一，便于后期维护
- 在腾讯地址页面中完整实现腾讯同步功能，包括进度显示和错误处理
- 移除原地址管理页面中所有腾讯同步相关的代码和引用

#### 功能迁移详情
**从原"地址管理"页面移除的功能：**
- 导入过程中的腾讯同步阶段（restapi/lbsproxy API调用）
- 腾讯同步进度显示和状态管理
- 相关的tencentApi引用和StoreData类型

**添加到"地址管理(腾讯)"页面的功能：**
- 完整的腾讯同步流程：数据导入 → KFC API获取信息 → 腾讯API同步
- 双进度条显示：数据录入进度 + 腾讯同步进度
- 完善的错误处理和状态管理
- 支持批量门店信息同步到腾讯地图服务

**用户体验优化：**
- 两个页面功能职责明确，避免混淆
- 腾讯地址页面专注于腾讯服务的集成
- 原地址页面专注于基础数据管理

### 后续优化调整 (完成日期：当前)

#### 优化1：原"地址管理"页面进度UI简化
- 移除腾讯同步相关的进度显示组件
- 简化UploadProgress组件，只显示数据录入进度
- 优化样式效果，提供更清晰的单步骤进度展示
- 在完成提示中添加腾讯同步功能的引导文字

#### 优化2："地址管理(腾讯)"页面流程简化
- 移除KFC API (emap/getStoreByCode) 调用及相关处理
- 简化Excel导入流程：直接读取数据 → 导入数据库 → 腾讯同步
- 移除handleAdd和handleUpdate函数中的门店信息自动获取功能
- 简化进度显示：只包含数据录入和腾讯同步两个主要阶段

#### 技术实现要点
- 保持接口兼容性，避免破坏现有功能
- 优化用户体验，提供更直观的进度反馈
- 简化代码逻辑，移除不必要的API调用
- 确保两个页面功能定位更加清晰明确

### 菜单结构优化 (完成日期：当前)

#### 用户需求
1. 菜单重命名：
   - "地址管理" → "百度地址管理"
   - "地址管理(腾讯)" → "腾讯地址管理"
2. 菜单结构调整：
   - 在"管理页"同级新增"地址管理"一级菜单
   - 将"百度地址管理"和"腾讯地址管理"设置为"地址管理"的二级菜单

#### 实现方案
- **路由结构调整**：修改`config/routes.ts`，将原来的两个平级路由改为嵌套结构
- **路径变更**：
  - 百度地址管理：`/address` → `/address/baidu`
  - 腾讯地址管理：`/address-tencent` → `/address/tencent`
- **默认重定向**：访问`/address`时自动重定向到`/address/baidu`
- **国际化配置**：更新中英文菜单翻译文件，添加新的菜单项

#### 技术实现要点
- 使用嵌套路由结构实现二级菜单
- 保持页面组件不变，只调整路由配置
- 添加适当的重定向，确保用户体验连贯
- 同时更新中英文国际化配置，保持多语言支持

#### 配置修复
**问题发现**：初始配置中路由name使用了错误的格式，导致菜单显示为原始key值而非翻译文本。

**解决方案**：
- 修正路由配置中的`name`字段：
  - `address.baidu` → `baidu`
  - `address.tencent` → `tencent`
- 修正国际化配置，确保使用正确的嵌套key格式：
  - 保持 `menu.address.baidu` 和 `menu.address.tencent`

**原理说明**：在UmiJS嵌套路由中，子路由的国际化key格式为`menu.{父路由name}.{子路由name}`，因此配置应为`menu.address.baidu`等。

### 腾讯地址管理进度条优化 (完成日期：当前)

#### 用户需求
将"腾讯地址管理"页面的上传进度条简化为单一进度条，样式效果与"百度地址管理"保持一致。

#### 实现方案
- **进度条合并**：将原来的数据导入和腾讯同步两个独立进度条合并为一个统一进度条
- **权重分配**：数据导入占总进度的60%，腾讯同步占40%
- **样式统一**：采用与"百度地址管理"相同的UI设计风格
- **状态优化**：根据当前阶段智能显示对应的状态文本和计数信息

#### 技术实现要点
- 实现总体进度计算算法，合理分配两个阶段的权重
- 保持接口兼容性，避免影响现有的状态管理逻辑
- 统一样式设计，包括颜色、字体、布局等细节
- 智能状态显示，根据当前阶段动态调整显示内容
- 优化用户体验，提供清晰直观的进度反馈

### 腾讯地址管理新增/编辑同步功能 (完成日期：当前)

#### 用户需求
在"腾讯地址管理"页面的新增和编辑功能中，也需要调用腾讯API将信息同步给腾讯服务。

#### 实现方案
- **新增功能增强**：在handleAdd函数中添加腾讯同步逻辑
- **编辑功能增强**：在handleUpdate函数中添加腾讯同步逻辑
- **两阶段处理**：
  1. 第一阶段：将数据保存到Supabase数据库
  2. 第二阶段：调用腾讯API同步数据到腾讯地图服务
- **用户反馈优化**：提供清晰的进度提示和结果反馈

#### 技术实现要点
- 使用`batchSyncStoresToTencent`API进行单个门店数据同步
- 实现分阶段的用户提示：数据保存成功 → 腾讯同步中 → 完成/失败
- 添加完善的错误处理：区分数据保存失败和腾讯同步失败
- 数据完整性检查：确保必要字段（name、longitude、latitude、address）齐全
- 异步处理和用户体验优化：使用loading提示，避免阻塞界面

#### 用户体验优化
- **成功场景**：显示"地址添加/更新和腾讯同步完成"
- **部分失败场景**：显示"地址操作成功，但腾讯同步失败，请稍后重试"
- **数据不完整场景**：显示"地址信息不完整，跳过腾讯同步"
- **完全失败场景**：显示具体的错误信息

---

## 需求17：腾讯API调用方式优化 (完成日期：当前)

### 用户需求
修改腾讯地址管理页的上传Excel功能中的腾讯API调用方式：
1. 将批量处理多个地址改为逐个调用kfc/restapi/lbsproxy接口
2. data入参从数组字符串改为对象字符串
3. 每次只传一个地址的信息

### 实现方案

#### API调用方式重构
- **原实现**：批量处理，每次最多30个门店，data参数为门店数组的JSON字符串
- **新实现**：逐个处理，每次只处理一个门店，data参数为单个门店对象的JSON字符串

#### 核心函数修改
- **原函数**：`syncStoreBatchToTencent(storesData: StoreData[])`
- **新函数**：`syncSingleStoreToTencent(storeData: StoreData)`
- 修改`batchSyncStoresToTencent`函数，改为逐个调用`syncSingleStoreToTencent`

#### data参数格式调整
- **原格式**：`data: JSON.stringify(tencentStoreItems)` (数组)
- **新格式**：`data: JSON.stringify(tencentStoreItem)` (单个对象)
- 单个门店对象包含：gas_data、name、lng、lat、addr、cpid、kfc_raw_id

#### 进度显示优化
- 修改进度回调信息，显示具体门店名称和序号
- 进度格式：`第X/总数个门店: 门店名称`
- 每个请求间添加200ms延迟，避免请求过于频繁

#### 技术实现要点
- 保持API接口签名不变，确保调用方代码兼容
- 改进日志输出，显示具体门店信息便于调试
- 减少单次请求延迟，从500ms改为200ms
- 移除批次概念，改为逐个处理模式
- 保持错误处理机制，确保单个门店失败不影响其他门店

### 影响说明
- 提高了API调用的可靠性，单个门店失败不影响其他门店
- 简化了data参数结构，更符合API设计规范
- 提供更精确的进度反馈，用户可以看到具体处理的门店信息
- 可能会增加总的处理时间，但提高了成功率和用户体验

---

## 需求18：系统品牌优化与管理页面清理 (完成日期：当前)

### 用户需求
对系统进行品牌化优化，删除不必要的管理页面：
1. 删除"管理页"以及其二级管理页
2. 更换所有Ant Design相关的内容和链接，替换为KFC相关内容

### 实现方案

#### 管理页面删除
- **路由清理**：从`config/routes.ts`中删除admin相关的路由配置
- **菜单清理**：从`src/locales/zh-CN/menu.ts`中删除管理页相关的菜单项
- **文件删除**：删除`src/pages/Admin.tsx`管理页面文件

#### 品牌内容替换
- **系统标题**：将"创新应用运营平台"更改为"KFC智能运营管理平台"
- **Logo配置**：使用本地logo文件替代外部logo链接
- **欢迎页面重构**：
  - 主标题：更改为"欢迎使用 KFC智能运营管理平台"
  - 描述文案：重写为KFC餐饮管理系统的介绍
  - 功能卡片：替换为门店地址管理、AI点餐反馈分析、数据统计报表

#### 链接和引用更新
- **Footer组件**：
  - 移除Ant Design Pro、GitHub、Ant Design官网链接
  - 新增KFC中国官网、关于我们、帮助中心链接
  - 更新版权信息为"KFC智能运营管理平台"
- **右侧帮助**：将帮助文档链接重定向到系统内部页面
- **Manifest配置**：更新应用名称和简称

#### 国际化文件清理
- 删除管理页相关的菜单翻译项
- 保持其他功能模块的翻译配置不变

### 技术实现要点
- 保持现有功能模块完整性，只删除管理页相关内容
- 系统化替换品牌元素，确保风格统一
- 移除未使用的导入项，修复linter警告
- 更新内部链接，避免外部依赖
- 保持系统架构和核心功能不变

### 影响范围
- **删除功能**：管理页及其子页面完全移除
- **品牌更新**：所有用户界面元素更新为KFC品牌风格
- **链接修正**：移除外部Ant Design链接，使用内部导航
- **用户体验**：更加符合KFC企业形象的系统界面

---

## 需求19：AI点餐反馈统计页面导出Excel功能 (完成日期：当前)

### 用户需求
在"AI点餐反馈统计"页面增加导出Excel功能，具体要求如下：
1. 和地址管理页一样，默认导出的是所有数据，勾选或筛选时依旧导出部分数据
2. 导出为excel时，其数据使用aiagent/feedback/query接口，分页查询，按每页20条数据逐步写入excel
3. 导出excel的字段和现在"AI点餐反馈统计"的列名字一致

### 实现方案

#### 导出功能设计
- **分页导出机制**：使用aiagent/feedback/query接口，每页20条数据逐步获取
- **导出模式支持**：
  - 默认导出：根据当前查询条件导出所有匹配数据
  - 选中导出：导出用户选中的记录行
- **字段映射**：与页面显示列名完全一致，包括反馈时间、ID、手机号、Session ID、问题类别、反馈内容、图片列表、视频

#### 核心功能实现
- **handleExport函数**：
  - 支持选中记录导出和全量导出两种模式
  - 实现分页循环获取数据，直到获取所有匹配记录
  - 使用xlsx库生成Excel文件，支持自定义列宽
  - 生成带时间戳的文件名：`ai_feedback_YYYY_MM_DD_HH_MM_SS.xlsx`

#### 数据处理优化
- **时间戳格式化**：将数字时间戳转换为中文本地化时间格式
- **图片URL处理**：解析JSON格式的图片URL列表，多个图片用逗号分隔
- **视频URL处理**：处理视频链接，无视频时显示"无视频"
- **Excel列宽优化**：根据内容长度设置合适的列宽

#### 用户界面增强
- **工具栏导出按钮**：在表格工具栏添加主导出按钮
- **行选择功能**：支持多行选择，选中时显示选中状态提醒
- **选中行操作**：提供"导出选中"和"取消选择"功能
- **按钮状态控制**：选中行时禁用主导出按钮，防止操作混淆

#### 查询参数管理
- **参数保存机制**：在表格request函数中保存当前查询参数
- **环境支持**：支持生产环境和测试环境的API调用
- **筛选条件支持**：支持手机号搜索和时间范围筛选的导出

#### 技术实现要点
- 使用React状态管理选中行和查询参数
- 实现异步分页数据获取，提供详细的进度日志
- 利用xlsx库的高级功能，包括列宽设置和工作表命名
- 完善的错误处理和用户提示机制
- 保持与现有签名认证机制的兼容性
- 优化用户体验，提供清晰的操作反馈

#### 功能特性
- **智能分页**：自动检测是否还有更多数据，循环获取直到完成
- **进度反馈**：控制台输出详细的获取进度信息
- **数据验证**：检查数据完整性，提供合适的提示信息
- **文件命名**：自动生成带时间戳的文件名，避免覆盖

---

## 需求20：基于用户角色的权限控制 (完成日期：当前)

### 用户需求
实现基于用户角色的权限控制功能，具体要求如下：
1. 使用supabase登录时返回的`ant_design_role`字段进行权限判断
2. 如果`ant_design_role`字段值为"admin"，表示管理员用户
3. 如果`ant_design_role`字段值为其它值（如"user"），表示普通用户
4. 非管理员用户无法访问地址管理页面（包括百度地址管理和腾讯地址管理）
5. 普通用户尝试跳转到地址管理页面时，需要阻止并提示"权限不足，请查看其它页面"

### 实现方案

#### 权限控制机制设计
- **权限定义**：在`src/access.ts`中添加`canManageAddress`权限定义
- **权限判断**：基于用户信息中的`access`字段（由`ant_design_role`映射而来）进行权限判断
- **管理员权限**：只有`access`字段值为"admin"的用户才能访问地址管理功能

#### 路由权限控制实现
- **路由配置**：在`config/routes.ts`中为地址管理相关路由添加`access: 'canManageAddress'`属性
- **权限覆盖**：
  - 地址管理一级菜单：添加`access: 'canManageAddress'`
  - 百度地址管理子页面：添加`access: 'canManageAddress'`
  - 腾讯地址管理子页面：添加`access: 'canManageAddress'`

#### 403权限不足页面实现
- **页面组件**：创建`src/pages/403.tsx`权限不足提示页面
- **UI设计**：使用Ant Design的Result组件，显示403状态和权限不足提示
- **用户引导**：提供"返回首页"按钮，引导用户访问有权限的页面
- **全局配置**：在`src/app.tsx`中启用`unAccessible`配置，使用自定义403页面

#### 用户体验优化
- **菜单隐藏**：普通用户登录时，地址管理菜单项自动隐藏，不会显示在导航中
- **直接访问阻止**：普通用户通过URL直接访问地址管理页面时，自动跳转到403页面
- **提示信息**：403页面显示明确的"权限不足，请查看其它页面"提示信息

#### 技术实现要点
- 利用Ant Design Pro的access插件实现权限控制
- 基于路由级别的权限校验，确保安全性
- 用户角色信息从Supabase的`user_profiles`表中的`ant_design_role`字段获取
- 权限判断逻辑集中在access.ts中，便于维护和扩展
- 保持现有登录认证流程不变，只在权限控制层面进行扩展

#### 权限控制流程
1. **用户登录**：Supabase认证成功后，获取用户的`ant_design_role`字段
2. **角色映射**：将`ant_design_role`字段值映射到用户信息的`access`字段
3. **权限验证**：访问地址管理页面时，系统检查用户的`canManageAddress`权限
4. **访问控制**：有权限则正常访问，无权限则显示403页面
5. **菜单控制**：根据权限动态显示或隐藏相关菜单项

---

## 需求21：用户管理页面 (完成日期：当前)

### 用户需求
创建一个新的"用户管理"页面，只有管理员权限的用户可以看到，具体功能如下：
1. 可以列出目前supabase里有的用户，展示列：user_profiles表里的用户昵称(name字段)、用户所属组(group字段)、Authentication中的登录用户名(Email字段)、上次登录时间(Last sign in at字段)
2. 页面里有新增按钮，可以增加用户，将增加的用户写入supabase
3. 用户列表后面有删除按钮，可以删除某个用户(不包含当前用户)

### 实现方案

#### Supabase用户数据服务实现
- 创建`supabase/users.ts`，实现用户管理相关的CRUD操作
- 实现`getUserList`函数，支持分页查询和条件筛选
- 使用supabaseAdmin客户端获取认证用户信息，合并用户资料和认证数据
- 实现`addUser`函数，使用管理员权限创建用户和用户资料
- 实现`updateUser`和`removeUser`函数，支持用户信息更新和删除
- 实现`getCurrentUserId`函数，获取当前登录用户ID

#### API服务层实现
- 创建`src/services/ant-design-pro/users.ts`，封装用户管理API
- 提供`queryUsers`、`createUser`、`updateUserInfo`、`deleteUser`等接口
- 导出完整的TypeScript类型定义，确保类型安全

#### 页面组件实现
- 创建`src/pages/UserManagement/index.tsx`作为主页面
- 使用ProTable组件实现用户列表展示、分页和搜索功能
- 支持按用户昵称、邮箱、用户组、角色进行筛选
- 实现用户角色的Tag标签展示，区分管理员和普通用户
- 添加操作列，支持编辑和删除功能，当前用户不能删除自己

#### 表单组件实现
- 创建`src/pages/UserManagement/components/CreateForm.tsx`新增用户表单
- 创建`src/pages/UserManagement/components/EditForm.tsx`编辑用户表单
- 表单包含邮箱、密码、用户昵称、用户组、角色等字段
- 编辑表单支持修改用户基本信息，但不包含邮箱和密码

#### 权限控制实现
- 在`src/access.ts`中添加`canManageUsers`权限定义
- 只有管理员用户(`access === 'admin'`)才能访问用户管理功能
- 在路由配置中添加`access: 'canManageUsers'`权限校验

#### 路由和菜单配置
- 在`config/routes.ts`中添加用户管理路由：`/user-management`
- 设置路由图标为`team`，体现用户管理的功能特征
- 添加中英文菜单翻译：
  - 中文：`menu.userManagement: 用户管理`
  - 英文：`menu.userManagement: User Management`

#### 功能特性
- **用户列表展示**：显示用户昵称、邮箱、用户组、角色、上次登录时间、创建时间
- **高级搜索**：支持按多个字段进行组合搜索和筛选
- **新增用户**：管理员可以创建新用户，设置邮箱、密码、昵称、组和角色
- **编辑用户**：支持修改用户的基本信息，如昵称、组、角色、个性签名等
- **删除用户**：支持删除用户，但不能删除当前登录用户，包含二次确认
- **权限保护**：非管理员用户无法访问此页面，会被重定向到403页面
- **数据安全**：使用Supabase Admin客户端进行用户创建和删除，确保操作安全性

#### 技术实现要点
- 使用Supabase Authentication Admin API进行用户认证管理
- 结合user_profiles表和auth.users表提供完整的用户信息
- 实现分页查询，优化大数据量场景下的性能
- 使用TypeScript严格类型定义，确保代码质量和类型安全
- 采用React Hooks管理组件状态，支持复杂的表单交互
- 实现完善的错误处理和用户友好的提示信息
- 支持响应式设计，适配不同屏幕尺寸

---

## 技术问题记录

### ProTable组件valueType与自定义render冲突问题 (发现日期：当前)

#### 问题描述
在使用Ant Design Pro的ProTable组件时，如果同时为列设置了`valueType`（如`'select'`、`'dateTime'`等）和`valueEnum`配置，再加上自定义的`render`函数，会导致自定义render函数接收到的不是预期的数据值，而是React元素对象。

#### 具体现象
- 设置了`valueType: 'select'`和`valueEnum`的列，自定义render函数的第一个参数`text`变成了React元素对象
- 控制台显示类似：`$$typeof: Symbol(react.element), key: null, props: {...}`
- 导致基于数据值的条件判断逻辑失效

#### 根本原因
ProTable在处理列配置时，会根据`valueType`和`valueEnum`自动应用内置的渲染逻辑，这会覆盖或干扰自定义的render函数，导致传递给render函数的参数不是原始数据。

#### 解决方案
1. **移除冲突配置**：删除`valueType`和`valueEnum`配置，只使用自定义render函数
2. **替代筛选方案**：使用`filters`和`onFilter`属性来实现列筛选功能
3. **示例代码**：
   ```typescript
   // 错误的配置方式
   {
     dataIndex: 'status',
     valueType: 'select',
     valueEnum: { ... },
     render: (text) => { ... }  // text会是React元素
   }
   
   // 正确的配置方式
   {
     dataIndex: 'status',
     render: (text) => { ... },  // text是原始数据值
     filters: [
       { text: '选项1', value: 'value1' },
       { text: '选项2', value: 'value2' }
     ],
     onFilter: (value, record) => record.status === value
   }
   ```

#### 最佳实践
- 对于需要自定义渲染逻辑的列，避免同时使用`valueType`和自定义render
- 如果需要筛选功能，优先使用`filters`配置而非`valueEnum`
- 在开发过程中注意检查render函数接收的参数类型
- 使用TypeScript严格类型检查可以更早发现此类问题

#### 适用场景
此问题在以下场景中常见：
- 用户角色显示（需要将角色代码转换为中文显示）
- 状态显示（需要自定义状态标签样式）
- 枚举值显示（需要特殊的格式化或样式）
- 任何需要基于数据值进行条件渲染的场景

---

## 需求22：用户管理页面功能完善 (完成日期：当前)

### 用户需求
完善用户管理页面的两个关键功能问题：
1. 编辑按钮功能无效：编辑后点确定，信息无法保存到supabase
2. 新增用户时user_profiles接口请求问题：auth/v1/admin/users请求成功，但rest/v1/user_profiles接口请求有问题，应该是入参问题(入参为ant_design_role，name, user_id， group： 取值用户团队或管理团队)

### 实现方案

#### 编辑功能修复
- **问题根源**：原`updateUser`函数使用的是`supabaseClient`而不是`supabaseAdmin`，普通用户权限无法直接更新`user_profiles`表
- **解决方案**：修改`supabase/users.ts`中的`updateUser`函数，改用`supabaseAdmin`客户端进行更新操作
- **权限提升**：使用管理员权限确保用户资料更新操作能够成功执行
- **错误处理**：保持原有的错误处理逻辑，提供友好的用户提示

#### 新增用户功能优化
- **group字段标准化**：
  - 修改新增表单和编辑表单中的group字段，从文本输入改为下拉选择
  - 提供标准选项："用户团队"和"管理团队"
  - 设置为必填字段，确保数据一致性
- **默认值优化**：新增用户时，group字段默认值设为"用户团队"
- **数据验证增强**：添加用户资料创建时的详细日志输出，便于调试和问题定位

#### 表单组件改进
- **CreateForm.tsx**：
  - 将group字段改为ProFormSelect组件
  - 添加必填验证规则
  - 提供预设的团队选项
- **EditForm.tsx**：
  - 同样将group字段改为ProFormSelect组件
  - 保持与新增表单一致的选项和验证

#### 数据库操作优化
- **入参数据结构**：
  - 明确user_profiles表插入数据的字段：user_id、name、group、ant_design_role
  - 添加详细的调试日志，输出实际插入的数据结构
  - 确保tags字段正确处理为数组类型
- **错误处理改进**：优化错误信息的捕获和显示，提供更准确的错误定位

#### 技术实现要点
- 使用supabaseAdmin客户端确保权限充足
- 标准化用户组数据，避免自由输入导致的数据不一致
- 添加必要的调试信息，便于问题排查
- 保持API接口的向后兼容性
- 优化用户体验，提供清晰的错误提示

#### 修复内容总结
1. **权限修复**：updateUser函数改用supabaseAdmin客户端
2. **表单优化**：group字段改为下拉选择，提供标准选项
3. **数据验证**：添加调试日志和必填字段验证
4. **用户体验**：提供更友好的错误提示和数据输入方式

### 影响范围
- **编辑功能**：管理员现在可以正常编辑用户信息并保存到数据库
- **新增功能**：用户组选择标准化，减少数据不一致问题
- **数据质量**：通过下拉选择确保group字段值的一致性
- **调试能力**：添加详细日志便于问题定位和解决

#### 后续优化：用户名输入方式调整 (完成日期：当前)

##### 用户需求
将新增用户时的"邮箱"字段改成"用户名"，向Supabase入参时，email取"用户名"+后缀"@163.com"。

##### 实现方案
- **表单字段调整**：
  - 将`CreateForm.tsx`中的email字段改为username字段
  - 更新字段标签为"用户名"
  - 调整验证规则：支持字母、数字、下划线，长度3-20位
- **数据转换处理**：
  - 在`handleAdd`函数中将username转换为email格式
  - 添加"@163.com"后缀构成完整邮箱地址
  - 移除原始username字段，保持API接口一致性
- **显示优化**：
  - 用户列表中的"登录用户名"列自动去掉"@163.com"后缀显示
  - 保持用户界面的简洁性和一致性

##### 技术实现要点
- 使用正则表达式验证用户名格式：`/^[a-zA-Z0-9_]{3,20}$/`
- 在提交前将用户名转换为标准邮箱格式
- 保持数据库存储格式不变，确保系统兼容性
- 界面显示时自动处理邮箱后缀，提升用户体验

##### 安全优化：禁用自动填充 (完成日期：当前)

**需求**：在新增用户表单中禁用用户名和密码字段的自动填充功能，提高安全性。

**实现方案**：
- **用户名字段**：添加`autoComplete: 'off'`属性，完全禁用浏览器自动填充
- **密码字段**：添加`autoComplete: 'new-password'`属性，提示浏览器这是新密码，避免使用已保存的密码

**技术实现**：
- 通过ProForm组件的`fieldProps`属性传递HTML原生的`autoComplete`属性
- 使用`'off'`值禁用用户名自动填充
- 使用`'new-password'`值优化密码输入体验，符合HTML5规范

---

## 需求23：用户管理页面功能修复 (完成日期：当前)

### 用户需求
修复用户管理页面的两个关键问题：
1. 删除用户功能无效，会提示"用户信息不完整，无法删除"
2. 编辑功能点确定无效，且需要移除"个性签名"、"职位"、"地址"字段

### 实现方案

#### 删除功能问题修复
- **问题分析**：删除用户时检查`record.id`和`record.user_id`是否存在，但可能某些记录缺少这些必要字段
- **解决方案**：
  - 分别检查`record.id`和`record.user_id`，提供更具体的错误提示
  - 添加详细的调试日志，输出用户记录的完整信息
  - 优化错误信息，区分不同类型的删除失败原因
- **调试优化**：
  - 控制台输出删除用户的完整记录信息
  - 记录具体的删除步骤和API调用结果
  - 提供更精确的错误定位信息

#### 编辑功能问题修复
- **表单字段优化**：
  - 移除`EditForm.tsx`中的"个性签名"（signature）字段
  - 移除"职位"（title）字段
  - 移除"地址"（address）字段
  - 保留核心字段：用户昵称、用户所属组、用户角色、手机号
- **数据处理优化**：
  - 在`updateUser`函数中添加详细的调试日志
  - 记录原始数据和处理后的数据
  - 移除更新时不应包含的`id`字段
  - 优化错误处理和结果反馈

#### 调试信息增强
- **用户列表获取**：确保每个用户记录都包含完整的`id`和`user_id`信息
- **更新操作**：添加详细的函数调用和Supabase操作日志
- **错误定位**：提供具体的错误类型和失败原因
- **操作反馈**：确保用户能够获得准确的操作结果提示

#### 技术实现要点
- 使用更精确的错误检查逻辑，分别验证不同的必要字段
- 在关键操作点添加`console.log`调试信息
- 优化数据处理流程，确保不传递不必要的字段
- 保持管理员权限的正确使用，确保操作权限充足
- 提供用户友好的错误提示和操作反馈

#### 修复内容总结
1. **删除功能**：添加详细错误检查和调试信息，改善错误提示
2. **编辑功能**：移除不需要的表单字段，优化数据处理和调试
3. **错误处理**：提供更准确的错误定位和用户反馈
4. **调试支持**：添加全面的日志输出，便于问题排查

### 影响范围
- **删除功能**：提供更准确的错误诊断，帮助定位数据问题
- **编辑功能**：简化表单结构，聚焦核心用户信息管理
- **用户体验**：更友好的错误提示和操作反馈
- **维护性**：详细的调试日志便于后续问题排查

#### 后续问题修复：数据ID问题调试 (完成日期：当前)

**问题现象**：删除和编辑功能提示"用户资料ID不存在，无法删除"，且没有请求任何Supabase接口。

**问题分析**：传递给操作函数的record对象中可能缺少`id`字段，导致前端验证失败，无法进行后续API调用。

**调试方案**：
- **数据源调试**：在`getUserList`函数中添加详细日志，查看Supabase返回的原始数据
- **数据合并调试**：检查用户资料和认证信息的合并过程
- **ProTable调试**：添加rowKey函数调试，查看表格接收到的数据结构
- **操作按钮调试**：在render函数中添加日志，查看传递给操作函数的record数据

**临时修复**：
- **灵活ID获取**：使用`record.id || record.user_id`作为profileId的备选方案
- **详细错误信息**：输出record对象的所有字段，便于问题定位
- **按钮状态优化**：在缺少user_id时禁用删除按钮
- **ProTable rowKey优化**：使用函数形式提供更灵活的行标识

**调试信息**：
- 完整的数据获取和处理链路日志
- 每个操作步骤的详细记录
- 数据结构和字段的完整输出
- API调用前的数据验证信息

#### 最终修复：数据库字段错误问题 (解决日期：当前)

**问题根源**：根据用户提供的错误信息，发现`user_profiles`表的主键是`user_id`而不是`id`，导致所有使用`id`字段进行数据库操作的功能都会失败。

**错误信息**：`'column user_profiles.id does not exist'`

**解决方案**：
- **数据库操作修正**：
  - 修改`updateUser`函数，将`.eq('id', id)`改为`.eq('user_id', userId)`
  - 修改`removeUser`函数，将`.eq('id', id)`改为`.eq('user_id', userId)`
  - 简化函数签名，删除不必要的双ID参数
- **API层调整**：
  - 更新`updateUserInfo`和`deleteUser`函数，都改为只接收`userId`参数
  - 移除多余的ID参数传递
- **前端调用修正**：
  - 删除和编辑功能直接使用`record.user_id`
  - 移除对`record.id`的依赖和回退逻辑
  - 简化错误验证，只检查`user_id`字段

**修复结果**：用户管理页面的删除和编辑功能现在应该能够正常工作，不再出现"column does not exist"错误。

#### 用户删除权限优化 (完成日期：当前)

**用户需求**：将删除权限从"当前账号不允许删除"改为"只要是管理员账号就不允许删除，也就是只能删除普通用户"。

**实现方案**：
- **删除条件调整**：将 `userId === currentUserId` 改为 `record.ant_design_role === 'admin'`
- **按钮状态控制**：删除按钮的禁用条件从检查当前用户改为检查用户角色
- **错误提示优化**：提示信息从"不能删除当前用户"改为"不能删除管理员用户"
- **代码清理**：移除不再使用的 `currentUserId` 状态和相关的获取逻辑

**技术实现**：
- 修改 `handleDelete` 函数中的权限检查逻辑
- 更新删除按钮的 `disabled` 属性，基于用户角色而非用户ID
- 清理未使用的导入和状态变量，消除linter警告

**最终效果**：现在系统只允许删除普通用户(`ant_design_role !== 'admin'`)，所有管理员用户都不能被删除，确保系统至少保留一个管理员账号。

---
