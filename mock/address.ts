import { Request, Response } from 'express';
import { parse } from 'url';

// 模拟地址数据
const genAddresses = (count: number) => {
  const addresses = [];
  const cities = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '南京', '重庆'];
  
  for (let i = 0; i < count; i++) {
    const cityIndex = i % cities.length;
    const cityName = cities[cityIndex];
    
    // 根据城市生成不同的经纬度范围
    const longitudeMap: Record<string, number> = {
      '北京': 116.4, 
      '上海': 121.4, 
      '广州': 113.2, 
      '深圳': 114.0, 
      '杭州': 120.1,
      '成都': 104.0, 
      '武汉': 114.3, 
      '西安': 108.9, 
      '南京': 118.7, 
      '重庆': 106.5
    };
    
    const latitudeMap: Record<string, number> = {
      '北京': 39.9, 
      '上海': 31.2, 
      '广州': 23.1, 
      '深圳': 22.5, 
      '杭州': 30.2,
      '成都': 30.6, 
      '武汉': 30.5, 
      '西安': 34.2, 
      '南京': 32.0, 
      '重庆': 29.5
    };
    
    const longitudeBase = longitudeMap[cityName] || 116.4; // 默认北京的经度
    const latitudeBase = latitudeMap[cityName] || 39.9; // 默认北京的纬度
    
    addresses.push({
      id: i + 1,
      name: `${cityName}分店${i + 1}`,
      storeCode: `${cityName.substring(0, 1).toUpperCase()}${100 + i}`,
      address: `${cityName}市${['朝阳区', '海淀区', '东城区', '西城区', '南山区'][i % 5]}${['东路', '西路', '南路', '北路', '中心'][i % 5]}${100 + i}号`,
      city: cityName,
      phone: `1${Math.floor(Math.random() * 9) + 3}${Math.random().toString().slice(2, 10)}`,
      longitude: longitudeBase + Math.random() * 0.1,
      latitude: latitudeBase + Math.random() * 0.1,
    });
  }
  
  return addresses;
};

// 生成15条地址记录
const addresses = genAddresses(25);

function getAddresses(req: Request, res: Response, u: string) {
  let realUrl = u;
  if (!realUrl || Object.prototype.toString.call(realUrl) !== '[object String]') {
    realUrl = req.url;
  }
  
  const { current = 1, pageSize = 10, keyword } = req.query;
  const params = parse(realUrl, true).query as unknown as {
    current: number;
    pageSize: number;
    keyword?: string;
  };
  
  let dataSource = [...addresses];
  
  // 关键字筛选
  if (keyword) {
    const searchKeyword = keyword.toString().toLowerCase();
    dataSource = dataSource.filter(
      (item) => 
        item.name.toLowerCase().includes(searchKeyword) || 
        item.address.toLowerCase().includes(searchKeyword) ||
        item.storeCode.toLowerCase().includes(searchKeyword)
    );
  }
  
  // 分页
  const startIndex = ((params.current || Number(current)) - 1) * (params.pageSize || Number(pageSize));
  const endIndex = startIndex + (params.pageSize || Number(pageSize));
  
  const result = {
    data: dataSource.slice(startIndex, endIndex),
    total: dataSource.length,
    success: true,
    pageSize: Number(pageSize),
    current: Number(current),
  };
  
  return res.json(result);
}

// 导出地址文件
function exportAddresses(req: Request, res: Response) {
  // 实际项目中，应该生成一个Excel文件并返回
  // 这里仅返回一个简单的文本文件模拟
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', 'attachment; filename=addresses.xlsx');
  res.send('This is a mock Excel file');
}

// 导入地址文件
function importAddresses(req: Request, res: Response) {
  res.json({
    success: true,
    message: '导入成功',
  });
}

// 添加地址
function addAddress(req: Request, res: Response) {
  res.json({
    success: true,
    data: req.body,
  });
}

// 更新地址
function updateAddress(req: Request, res: Response) {
  res.json({
    success: true,
    data: req.body,
  });
}

// 删除地址
function removeAddress(req: Request, res: Response) {
  res.json({
    success: true,
    data: {},
  });
}

export default {
  'GET /api/address/list': getAddresses,
  'GET /api/address/export': exportAddresses,
  'POST /api/address/import': importAddresses,
  'POST /api/address/add': addAddress,
  'POST /api/address/update': updateAddress,
  'POST /api/address/remove': removeAddress,
}; 