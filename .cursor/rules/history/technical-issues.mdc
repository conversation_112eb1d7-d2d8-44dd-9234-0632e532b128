---
description: 
globs: 
alwaysApply: false
---
# 技术问题记录 - 汇总与解决方案

## 问题分类索引
- [TypeScript相关](mdc:#typescript相关问题)
- [Ant Design Pro相关](mdc:#ant-design-pro相关问题)
- [Supabase相关](mdc:#supabase相关问题)
- [API调用相关](mdc:#api调用相关问题)
- [环境配置相关](mdc:#环境配置相关问题)

## TypeScript相关问题

### 问题1：TypeScript类型声明冲突
**问题描述**：在`typings.d.ts`中使用`declare module './Editor'`与同名`.tsx`文件发生类型冲突

**错误信息**：`标识符"Editor"重复。ts(2300)`

**根本原因**：TypeScript声明文件与源文件在相同模块路径下有重复的默认导出

**解决方案**：
```typescript
// 错误的做法
declare module './Editor' {
  export default Editor;
}

// 正确的做法
export interface EditorProps {
  // 接口定义
}
```

**最佳实践**：
- 避免在`.d.ts`文件中为已存在的同名`.tsx`文件声明模块
- 使用常规的导出/导入模式共享类型
- 声明文件主要用于第三方库类型定义

**相关页面**：地址管理页面组件

---

## Ant Design Pro相关问题

### 问题2：ProTable组件valueType与自定义render冲突
**问题描述**：同时设置`valueType`、`valueEnum`和自定义`render`函数时，render函数接收到React元素而非原始数据

**错误现象**：
```typescript
// render函数接收到的不是字符串，而是React元素对象
{$$typeof: Symbol(react.element), key: null, props: {...}}
```

**根本原因**：ProTable根据`valueType`和`valueEnum`自动应用内置渲染逻辑，覆盖自定义render函数的参数

**解决方案**：
```typescript
// 错误配置
{
  dataIndex: 'status',
  valueType: 'select',
  valueEnum: { ... },
  render: (text) => { ... }  // text是React元素
}

// 正确配置
{
  dataIndex: 'status',
  render: (text) => { ... },  // text是原始数据
  filters: [
    { text: '选项1', value: 'value1' },
    { text: '选项2', value: 'value2' }
  ],
  onFilter: (value, record) => record.status === value
}
```

**最佳实践**：
- 需要自定义渲染逻辑时避免使用`valueType`
- 使用`filters`配置实现筛选功能
- 开发时检查render函数参数类型

**相关页面**：用户管理页面

---

## Supabase相关问题

### 问题3：数据库字段错误导致操作失败
**问题描述**：`user_profiles`表主键是`user_id`而非`id`，导致更新和删除操作失败

**错误信息**：`'column user_profiles.id does not exist'`

**根本原因**：代码中使用了错误的字段名进行数据库查询

**解决方案**：
```typescript
// 错误的操作
.eq('id', id)

// 正确的操作  
.eq('user_id', userId)
```

**最佳实践**：
- 明确了解数据表结构和主键字段
- 使用统一的字段命名约定
- 在开发环境中进行充分测试

**相关页面**：用户管理页面

### 问题4：权限不足导致数据操作失败
**问题描述**：使用普通客户端权限无法更新`user_profiles`表

**根本原因**：Supabase RLS策略限制了普通用户对用户资料表的操作权限

**解决方案**：
```typescript
// 错误：使用普通客户端
const { error } = await supabaseClient
  .from('user_profiles')
  .update(data)
  .eq('user_id', userId);

// 正确：使用管理员客户端
const { error } = await supabaseAdmin
  .from('user_profiles')
  .update(data)
  .eq('user_id', userId);
```

**最佳实践**：
- 用户管理操作使用管理员权限客户端
- 普通业务操作使用普通客户端
- 明确区分不同权限级别的使用场景

**相关页面**：用户管理页面

---

## API调用相关问题

### 问题5：POST请求方式与API文档不符
**问题描述**：AI反馈API实际需要POST请求，但初始实现使用GET方式

**解决方案**：
```typescript
// 错误：GET请求
const response = await request(url, {
  method: 'GET',
  params: queryParams
});

// 正确：POST请求
const response = await request(url, {
  method: 'POST',
  data: requestBody
});
```

**相关调整**：
- 修改请求方式为POST
- 参数从URL查询字符串改为请求体
- 调整签名计算逻辑

**相关页面**：AI反馈统计页面

### 问题6：URL参数污染问题
**问题描述**：全局请求拦截器自动添加token参数，影响特定API调用

**解决方案**：
```typescript
// 在请求拦截器中添加例外处理
if (url.includes('/agent-service/aiagent/feedback/query')) {
  // 跳过token参数添加
  return url;
}
```

**最佳实践**：
- 为特殊API添加例外处理逻辑
- 避免全局配置影响特定接口
- 明确区分不同认证机制的API

**相关页面**：AI反馈统计页面

### 问题7：API签名路径计算错误
**问题描述**：签名计算使用完整URL路径而非相对路径，导致签名验证失败

**解决方案**：
```typescript
// 错误：使用完整路径
const signPath = '/agent-service/aiagent/feedback/query';

// 正确：使用相对路径
const signPath = '/aiagent/feedback/query';
const fullPath = '/agent-service/aiagent/feedback/query';
```

**最佳实践**：
- 严格按照API文档要求构建签名
- 分离签名路径和请求路径
- 在测试环境验证签名机制

**相关页面**：AI反馈统计页面

---

## 环境配置相关问题

### 问题8：环境变量在前端无法获取
**问题描述**：`.env`文件中的环境变量在前端代码中无法访问

**根本原因**：前端应用中的`process.env`不会自动读取`.env`文件

**解决方案**：
```typescript
// config/config.ts中添加
import dotenv from 'dotenv';

const envResult = dotenv.config();
const defineEnvVars: Record<string, any> = {};

if (envResult.parsed) {
  Object.keys(envResult.parsed).forEach((key) => {
    defineEnvVars[`process.env.${key}`] = process.env[key];
  });
}

export default {
  define: defineEnvVars,
  // 其他配置
};
```

**最佳实践**：
- 使用UmiJS的define配置注入环境变量
- 实现自动化环境变量注入机制
- 为关键配置提供备用值
- 区分开发和生产环境配置

**相关组件**：Supabase客户端初始化

---

## 性能相关问题

### 问题9：大数据量导出性能问题
**问题描述**：一次性获取大量数据进行导出时响应缓慢

**解决方案**：
```typescript
// 分批获取数据
const batchSize = 20;
let allData = [];
let hasMore = true;
let page = 1;

while (hasMore) {
  const response = await queryAPI({
    current: page,
    pageSize: batchSize,
    ...searchParams
  });
  
  allData = allData.concat(response.data);
  hasMore = response.data.length === batchSize;
  page++;
}
```

**最佳实践**：
- 使用分页机制逐步获取数据
- 提供进度反馈给用户
- 设置合理的批次大小
- 添加取消操作功能

**相关页面**：AI反馈统计页面、地址管理页面

---

## 调试建议

### 通用调试方法
1. **浏览器开发者工具**：
   - Network标签检查API请求和响应
   - Console标签查看错误信息和日志
   - Elements标签检查DOM结构和样式

2. **代码调试**：
   - 使用`console.log`记录关键变量值
   - 使用`debugger`语句设置断点
   - 添加try-catch捕获异常信息

3. **API调试**：
   - 使用Postman等工具测试API接口
   - 检查请求头、参数格式和认证信息
   - 对比成功和失败请求的差异

### 预防措施
1. **类型安全**：使用TypeScript严格模式
2. **错误处理**：为所有异步操作添加错误处理
3. **测试覆盖**：编写单元测试和集成测试
4. **代码审查**：团队成员之间进行代码审查

---

## 问题上报模板
当遇到新的技术问题时，请按以下格式记录：

```markdown
### 问题X：问题简短描述
**问题描述**：详细描述问题现象和影响

**错误信息**：完整的错误信息或日志

**复现步骤**：
1. 步骤1
2. 步骤2
3. 步骤3

**根本原因**：问题的技术原因分析

**解决方案**：具体的解决代码或配置

**最佳实践**：避免类似问题的建议

**相关页面**：受影响的页面或组件
