---
description: 
globs: 
alwaysApply: false
---
# AI编辑工具更新机制指导

## 📋 重要提醒：功能完成后必须更新Rules

**致所有AI助手（Cursor、GitHub Copilot等）**：

每当完成功能开发或问题修复后，**必须立即更新对应的rules文件**，以确保知识沉淀和团队协作效率。

## 🔄 更新流程

### 1. 功能开发完成后
当你完成以下任何工作时，需要更新对应的rules文件：

#### ✅ 需要更新的情况
- ✨ 新增页面功能
- 🐛 修复技术问题  
- 🔧 优化现有功能
- 📦 集成新的API或服务
- 🎨 UI/UX改进
- 🔒 权限控制调整
- 📊 数据结构变更

#### ❌ 无需更新的情况
- 🔍 仅查看代码
- 💬 回答技术问题
- 📖 阅读文档
- 🧹 代码格式化

### 2. 更新目标文件

根据你的改动内容，更新对应的rules文件：

#### 页面级更新 (.cursor/rules/pages/)
```
用户管理功能 → user-management.mdc
AI反馈统计 → ai-feedback.mdc  
百度地址管理 → address-baidu.mdc
腾讯地址管理 → address-tencent.mdc
欢迎页面 → welcome.mdc
```

#### 共享功能更新 (.cursor/rules/shared/)
```
API服务 → api-services.mdc
公共组件 → components.mdc
数据库操作 → supabase.mdc
工具函数 → utils.mdc
```

#### 技术问题记录 (.cursor/rules/history/)
```
技术问题解决 → technical-issues.mdc
系统迁移记录 → migration-records.mdc
```

### 3. 更新内容模板

#### 功能迭代记录模板
```markdown
### v1.x.x (功能名称 - 更新日期)
**新增功能**：
- 具体的功能描述
- 技术实现要点
- 用户体验改进

**技术实现**：
- 关键技术点
- API接口变更
- 数据结构调整

**影响范围**：
- 影响的页面和功能
- 依赖关系变化
```

#### 技术问题记录模板
```markdown
### 问题X：问题标题 (解决日期：YYYY-MM-DD)

**问题描述**：
详细描述遇到的技术问题，包括错误信息和复现步骤

**根本原因**：
分析问题的根本原因，技术层面的深层次原因

**解决方案**：
```typescript
// 展示具体的代码解决方案
```

**最佳实践**：
- 避免同类问题的建议
- 相关的开发规范
- 性能或安全考虑

**相关页面**：影响的具体页面或模块
```

## 🎯 更新要求

### 必须包含的信息
1. **版本号**：使用语义化版本号 (v1.0.0, v1.1.0等)
2. **更新日期**：使用YYYY-MM-DD格式
3. **功能描述**：清晰描述新增或修改的功能
4. **技术要点**：关键的技术实现细节
5. **影响范围**：说明影响的页面、API、数据结构等

### 写作规范
- ✅ 使用清晰简洁的中文描述
- ✅ 包含具体的技术细节和代码示例
- ✅ 提供问题的根本原因分析
- ✅ 给出可操作的解决方案
- ❌ 避免模糊不清的描述
- ❌ 不要遗漏重要的技术细节

## 🔧 具体操作步骤

### 步骤1：确定更新文件
根据你的改动，确定需要更新的rules文件路径

### 步骤2：添加新版本记录
在文件的"历史迭代记录"部分，添加新的版本记录

### 步骤3：更新技术问题记录（如果适用）
如果解决了技术问题，在"已解决的技术问题"部分添加记录

### 步骤4：检查更新质量
确保信息完整、准确、有价值

## 💡 更新价值

### 对团队的价值
- 📚 **知识沉淀**：避免重复踩坑，提高开发效率
- 🔍 **问题追溯**：快速定位历史问题和解决方案  
- 📈 **技能提升**：团队成员互相学习最佳实践
- 🚀 **快速上手**：新成员快速了解系统演进历史

### 对AI助手的价值
- 🧠 **上下文理解**：更好地理解项目历史和决策背景
- 🎯 **精准建议**：基于历史经验提供更准确的解决方案
- 🔄 **连续性**：保持对话间的知识连贯性
- 📊 **模式识别**：识别常见问题模式和解决思路

## ⚡ 快速检查清单

每次完成开发任务时，请检查：

- [ ] 是否新增了功能？ → 更新对应页面rules
- [ ] 是否修复了bug？ → 更新技术问题记录  
- [ ] 是否改动了API？ → 更新api-services.mdc
- [ ] 是否调整了数据库？ → 更新supabase.mdc
- [ ] 是否优化了组件？ → 更新components.mdc
- [ ] 是否解决了性能问题？ → 更新technical-issues.mdc

## 📝 提醒机制

**在完成任何开发任务的最后，请务必问自己：**

> "我需要更新哪些rules文件来记录这次的工作？"

**如果答案是"需要"，请立即执行更新操作！**

---

## 🎯 结语

这个更新机制是为了确保项目知识的持续积累和团队协作效率的提升。每一次及时的更新，都是对未来工作的投资！

**记住：好的文档是最好的传承！** 📖✨
