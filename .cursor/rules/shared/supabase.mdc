---
description: 
globs: 
alwaysApply: false
---
# Supabase数据库 - 操作规范与最佳实践

## 架构概述
### 服务结构
```
supabase/
├── index.ts                     # 客户端初始化和配置
├── env-check.ts                 # 环境变量检查工具
├── auth.ts                      # 认证服务
├── users.ts                     # 用户管理数据服务
├── address.ts                   # 百度地址数据服务
├── address_tencent.ts           # 腾讯地址数据服务
├── scripts/                     # 数据库脚本
│   ├── init-db.ts              # 数据库初始化
│   ├── mock-data.ts            # 测试数据生成
│   └── test-env.ts             # 环境测试
└── README.md                   # 服务说明文档
```

### 客户端配置
```typescript
// 双客户端模式
export const supabaseClient = createClient(url, anonKey);    // 普通权限
export const supabaseAdmin = createClient(url, serviceKey); // 管理员权限

// 权限使用原则
- supabaseClient: 用户数据查询、业务数据CRUD
- supabaseAdmin: 用户管理、系统管理、权限操作
```

## 数据表结构规范

### 用户相关表
#### auth.users (Supabase内置认证表)
```sql
-- 主要字段
id: uuid (主键)
email: varchar (邮箱)
created_at: timestamptz
last_sign_in_at: timestamptz
user_metadata: jsonb
```

#### user_profiles (用户资料表)
```sql
-- 表结构
user_id: uuid (主键，关联auth.users.id)
name: varchar (用户昵称)
group: varchar (用户组：用户团队/管理团队)
ant_design_role: varchar (角色：admin/user)
phone: varchar (手机号，可选)
created_at: timestamptz
updated_at: timestamptz

-- 索引
CREATE INDEX idx_user_profiles_role ON user_profiles(ant_design_role);
CREATE INDEX idx_user_profiles_group ON user_profiles(group);
```

### 地址相关表
#### address_baidu (百度地址表)
```sql
-- 表结构
id: serial (主键)
storeCodeId: varchar (门店ID)
name: varchar (门店名称)
address: varchar (地址)
cityName: varchar (城市)
tel: varchar (电话)
longitude: decimal (经度)
latitude: decimal (纬度)
storeCodePoiid: varchar (子点poiid)
marketName: varchar (市场)
parentName: varchar (主点名称)
parentStoreCodeId: varchar (母店号)
parentStoreCodePoiid: varchar (主点poiid)
created_at: timestamptz
updated_at: timestamptz

-- 索引
CREATE INDEX idx_address_baidu_store_code ON address_baidu(storeCodeId);
CREATE INDEX idx_address_baidu_city ON address_baidu(cityName);
CREATE INDEX idx_address_baidu_updated ON address_baidu(updated_at DESC);
```

#### address_tencent (腾讯地址表)
```sql
-- 表结构（与address_baidu相同）
-- 用于腾讯地图服务的独立数据管理
```

## 数据操作规范

### CRUD操作模式
```typescript
// 标准查询模式
export async function getRecords(params?: PaginationParams) {
  try {
    let query = supabaseClient
      .from(TABLE_NAME)
      .select('*', { count: 'exact' });

    // 添加搜索条件
    if (params?.search) {
      query = query.or(`name.ilike.%${params.search}%,address.ilike.%${params.search}%`);
    }

    // 添加时间范围筛选
    if (params?.startTime) {
      query = query.gte('created_at', params.startTime);
    }
    if (params?.endTime) {
      query = query.lte('created_at', params.endTime);
    }

    // 分页和排序
    const pageSize = params?.pageSize || 20;
    const current = params?.current || 1;
    const from = (current - 1) * pageSize;
    const to = from + pageSize - 1;

    query = query
      .order('updated_at', { ascending: false })
      .range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('查询失败:', error);
      throw new Error(error.message);
    }

    return { data: data || [], total: count || 0 };
  } catch (error) {
    console.error('数据查询异常:', error);
    throw error;
  }
}
```

### 新增数据规范
```typescript
export async function createRecord(record: CreateParams) {
  try {
    // 数据预处理
    const processedRecord = {
      ...record,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabaseClient
      .from(TABLE_NAME)
      .insert([processedRecord])
      .select()
      .single();

    if (error) {
      console.error('新增失败:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('新增记录异常:', error);
    throw error;
  }
}
```

### 更新数据规范
```typescript
export async function updateRecord(id: string, updates: UpdateParams) {
  try {
    // 移除不应更新的字段
    const { created_at, id: recordId, ...updateData } = updates;
    
    // 添加更新时间
    const processedUpdates = {
      ...updateData,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabaseClient
      .from(TABLE_NAME)
      .update(processedUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('更新失败:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('更新记录异常:', error);
    throw error;
  }
}
```

### 删除数据规范
```typescript
export async function deleteRecord(id: string) {
  try {
    const { error } = await supabaseClient
      .from(TABLE_NAME)
      .delete()
      .eq('id', id);

    if (error) {
      console.error('删除失败:', error);
      throw new Error(error.message);
    }

    return { success: true };
  } catch (error) {
    console.error('删除记录异常:', error);
    throw error;
  }
}
```

## 认证服务规范

### 用户登录
```typescript
export async function signIn(email: string, password: string) {
  try {
    // 1. Supabase认证
    const { data: authData, error: authError } = await supabaseClient.auth
      .signInWithPassword({ email, password });

    if (authError) {
      throw new Error(authError.message);
    }

    // 2. 获取用户资料
    const { data: profile, error: profileError } = await supabaseClient
      .from('user_profiles')
      .select('*')
      .eq('user_id', authData.user.id)
      .single();

    if (profileError) {
      console.error('获取用户资料失败:', profileError);
      // 不抛出错误，使用默认资料
    }

    // 3. 合并用户信息
    return {
      ...authData.user,
      profile: profile || { name: email.split('@')[0], ant_design_role: 'user' }
    };
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
}
```

### 用户管理（管理员权限）
```typescript
export async function createUser(userData: CreateUserParams) {
  try {
    // 1. 创建认证用户（使用管理员权限）
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin
      .createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
      });

    if (authError) {
      throw new Error(authError.message);
    }

    // 2. 创建用户资料
    const profileData = {
      user_id: authUser.user.id,
      name: userData.name,
      group: userData.group,
      ant_design_role: userData.ant_design_role,
      phone: userData.phone || null,
    };

    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .insert([profileData])
      .select()
      .single();

    if (profileError) {
      // 回滚认证用户创建
      await supabaseAdmin.auth.admin.deleteUser(authUser.user.id);
      throw new Error(profileError.message);
    }

    return { user: authUser.user, profile };
  } catch (error) {
    console.error('创建用户失败:', error);
    throw error;
  }
}
```

## 错误处理规范

### 统一错误处理
```typescript
// 错误类型定义
interface SupabaseError {
  message: string;
  code?: string;
  details?: string;
  hint?: string;
}

// 错误处理工具函数
export function handleSupabaseError(error: SupabaseError, operation: string) {
  console.error(`${operation}失败:`, error);
  
  // 根据错误代码返回用户友好的错误信息
  switch (error.code) {
    case 'PGRST116':
      return '数据不存在或已被删除';
    case '23505':
      return '数据已存在，请检查唯一性约束';
    case '23503':
      return '关联数据不存在，请检查数据完整性';
    case '42501':
      return '权限不足，无法执行此操作';
    default:
      return error.message || '操作失败，请稍后重试';
  }
}
```

### 重试机制
```typescript
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      console.warn(`操作失败，第${attempt}次重试...`, error);
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw new Error('重试次数已用完');
}
```

## 性能优化规范

### 查询优化
```typescript
// 1. 选择性查询字段
const { data } = await supabaseClient
  .from('table_name')
  .select('id, name, created_at') // 只查询需要的字段
  .limit(100);

// 2. 使用索引优化查询
const { data } = await supabaseClient
  .from('address_baidu')
  .select('*')
  .eq('storeCodeId', storeId) // 使用索引字段
  .order('updated_at', { ascending: false }); // 使用索引排序

// 3. 分页查询避免性能问题
const pageSize = 20; // 合理的分页大小
const { data } = await supabaseClient
  .from('table_name')
  .select('*')
  .range(start, end)
  .limit(pageSize);
```

### 批量操作优化
```typescript
// 批量插入
export async function batchInsert<T>(records: T[], batchSize: number = 100) {
  const results = [];
  
  for (let i = 0; i < records.length; i += batchSize) {
    const batch = records.slice(i, i + batchSize);
    
    const { data, error } = await supabaseClient
      .from(TABLE_NAME)
      .insert(batch)
      .select();
    
    if (error) {
      console.error(`批次 ${i / batchSize + 1} 插入失败:`, error);
      throw error;
    }
    
    results.push(...(data || []));
    
    // 添加延迟避免频率限制
    if (i + batchSize < records.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return results;
}
```

## 安全规范

### Row Level Security (RLS)
```sql
-- 用户资料表安全策略
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- 用户只能查看自己的资料
CREATE POLICY "用户查看自己资料" ON user_profiles
  FOR SELECT USING (auth.uid() = user_id);

-- 管理员可以查看所有用户资料
CREATE POLICY "管理员查看所有资料" ON user_profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.uid() 
      AND ant_design_role = 'admin'
    )
  );
```

### 数据验证
```typescript
// 输入数据验证
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validateStoreCode(storeCode: string): boolean {
  const storeCodeRegex = /^[A-Z0-9_]{3,20}$/;
  return storeCodeRegex.test(storeCode);
}

// 数据清理
export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>'"]/g, '');
}
```

## 测试规范

### 单元测试
```typescript
// 使用Jest测试Supabase服务
import { getRecords, createRecord } from '../address';

// Mock Supabase客户端
jest.mock('../index', () => ({
  supabaseClient: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  },
}));

describe('Address Service', () => {
  it('应该正确获取地址列表', async () => {
    const mockData = [{ id: 1, name: '测试门店' }];
    supabaseClient.single.mockResolvedValue({ data: mockData, error: null });
    
    const result = await getRecords();
    expect(result.data).toEqual(mockData);
  });
});
```

### 集成测试
```typescript
// 测试真实Supabase连接
describe('Supabase Integration', () => {
  beforeAll(async () => {
    // 设置测试环境
    process.env.SUPABASE_URL = 'test-url';
    process.env.SUPABASE_ANON_KEY = 'test-key';
  });
  
  it('应该能够连接到Supabase', async () => {
    const { data, error } = await supabaseClient
      .from('address_baidu')
      .select('count(*)')
      .limit(1);
    
    expect(error).toBeNull();
    expect(data).toBeDefined();
  });
});
```

## 部署和维护

### 环境变量管理
```typescript
// 环境检查
export function checkSupabaseEnv() {
  const requiredEnvs = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missing = requiredEnvs.filter(env => !process.env[env]);
  
  if (missing.length > 0) {
    throw new Error(`缺少环境变量: ${missing.join(', ')}`);
  }
  
  console.log('✅ Supabase环境变量检查通过');
}
```

### 数据库迁移
```typescript
// 数据迁移脚本示例
export async function migrateData() {
  try {
    // 1. 备份现有数据
    const { data: backup } = await supabaseAdmin
      .from('old_table')
      .select('*');
    
    // 2. 转换数据格式
    const transformedData = backup?.map(record => ({
      // 数据转换逻辑
    }));
    
    // 3. 插入新表
    const { error } = await supabaseAdmin
      .from('new_table')
      .insert(transformedData);
    
    if (error) throw error;
    
    console.log('✅ 数据迁移完成');
  } catch (error) {
    console.error('❌ 数据迁移失败:', error);
    throw error;
  }
}
```

## 最佳实践总结

### 操作原则
1. **权限最小化**：根据操作类型选择合适的客户端权限
2. **数据完整性**：确保关联数据的一致性
3. **错误处理**：提供详细的错误信息和恢复建议
4. **性能优化**：合理使用索引和分页查询

### 安全要求
1. **敏感信息**：API密钥等敏感信息不能暴露在前端
2. **数据验证**：所有输入数据必须经过验证和清理
3. **权限控制**：使用RLS策略控制数据访问权限
4. **审计日志**：记录关键操作的审计信息

### 维护建议
1. **定期备份**：重要数据定期备份到外部存储
2. **性能监控**：监控查询性能和数据库使用情况
3. **版本管理**：数据库结构变更使用版本控制
4. **文档更新**：及时更新API文档和操作说明
