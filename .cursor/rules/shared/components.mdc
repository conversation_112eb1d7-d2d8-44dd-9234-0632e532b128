---
description: 
globs: 
alwaysApply: false
---
# 公共组件 - 设计规范与使用指南

## 组件架构概述
### 组件分层结构
```
src/components/                   # 全局公共组件
├── Footer/                      # 页脚组件
├── RightContent/                # 右侧内容组件
│   ├── AvatarDropdown.tsx      # 用户头像下拉菜单
│   └── index.tsx               # 右侧内容整合
├── HeaderDropdown/              # 头部下拉组件
└── index.ts                    # 组件统一导出

pages/{PageName}/components/     # 页面级组件
├── CreateForm.tsx              # 新增表单组件
├── Editor.tsx                  # 编辑表单组件
├── UploadProgress.tsx          # 上传进度组件
└── typings.d.ts               # 类型定义（避免与tsx文件冲突）
```

## 全局公共组件

### Footer 页脚组件
**功能定位**：系统页脚信息展示
```typescript
// 使用方式
import { Footer } from '@/components';

// 特性
- 版权信息显示
- 企业链接导航
- 固定在页面底部
- 响应式布局适配
```

**设计规范**：
- 使用KFC品牌链接和版权信息
- 移除第三方框架相关链接
- 保持简洁的视觉设计
- 支持多语言显示

### RightContent 右侧内容组件
**功能定位**：顶部导航栏右侧功能区域
```typescript
// 包含子组件
- AvatarDropdown: 用户头像和下拉菜单
- 其他导航工具（如通知、设置等）

// 特性
- 用户信息展示
- 登出功能
- 个人设置入口
- 消息通知（可扩展）
```

### HeaderDropdown 头部下拉组件
**功能定位**：可复用的下拉菜单组件
```typescript
// 使用方式
<HeaderDropdown overlay={menuContent}>
  <span>触发元素</span>
</HeaderDropdown>

// 特性
- 统一的下拉样式
- 悬停和点击交互
- 位置自动调整
- 可自定义内容
```

## 页面级组件规范

### 表单组件设计规范
#### CreateForm 新增表单
```typescript
// 标准接口定义
interface CreateFormProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onFinish: (values: any) => Promise<void>;
  initialValues?: Record<string, any>;
}

// 设计要求
- 使用ProForm或ModalForm组件
- 必填字段明确标注
- 表单验证规则完整
- 提交按钮状态管理
- 错误处理和用户反馈
```

#### Editor 编辑表单
```typescript
// 标准接口定义
interface EditorProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onFinish: (values: any) => Promise<void>;
  initialValues: Record<string, any>;
  record: any;
}

// 设计要求
- 继承CreateForm的设计规范
- 支持初始值回填
- 区分新增和编辑状态
- 提供重置和取消功能
```

#### UploadProgress 上传进度组件
```typescript
// 状态定义
type ProgressStatus = 'uploading' | 'importing' | 'syncing' | 'completed' | 'error';

interface UploadProgressProps {
  visible: boolean;
  status: ProgressStatus;
  progress: number;
  onCancel?: () => void;
  title?: string;
  description?: string;
}

// 设计要求
- 精美的进度条UI设计
- 实时进度更新
- 状态图标和文字提示
- 支持取消操作
- 错误状态处理
```

## 组件开发规范

### TypeScript类型定义
```typescript
// 基础Props接口
interface BaseProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

// 表单组件Props
interface FormProps extends BaseProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onFinish: (values: any) => Promise<void>;
  loading?: boolean;
}

// 数据组件Props
interface DataProps<T> extends BaseProps {
  dataSource: T[];
  loading?: boolean;
  pagination?: PaginationConfig;
  onPageChange?: (page: number, pageSize: number) => void;
}
```

### 组件命名规范
- **组件文件**：使用PascalCase命名（如`CreateForm.tsx`）
- **组件名称**：与文件名保持一致
- **Props接口**：组件名 + `Props`（如`CreateFormProps`）
- **样式文件**：使用camelCase命名（如`createForm.less`）

### 组件结构规范
```typescript
// 标准组件结构
import React from 'react';
import { ProForm, ModalForm } from '@ant-design/pro-components';
import type { CreateFormProps } from './typings';

const CreateForm: React.FC<CreateFormProps> = (props) => {
  // 1. 解构props
  const { visible, onVisibleChange, onFinish, ...restProps } = props;
  
  // 2. 状态定义
  const [loading, setLoading] = useState(false);
  
  // 3. 事件处理函数
  const handleFinish = async (values: any) => {
    setLoading(true);
    try {
      await onFinish(values);
      onVisibleChange(false);
    } catch (error) {
      // 错误处理
    } finally {
      setLoading(false);
    }
  };
  
  // 4. 渲染JSX
  return (
    <ModalForm
      title="新增"
      open={visible}
      onOpenChange={onVisibleChange}
      onFinish={handleFinish}
      loading={loading}
      {...restProps}
    >
      {/* 表单内容 */}
    </ModalForm>
  );
};

export default CreateForm;
```

## 样式设计规范

### CSS类命名约定
```less
// 使用BEM命名规范
.component-name {           // 组件基础类
  &__element {             // 元素类
    &--modifier {          // 修饰符类
    }
  }
  
  &--variant {             // 组件变体类
  }
}

// 示例
.upload-progress {
  &__header {
    &--success { color: @success-color; }
    &--error { color: @error-color; }
  }
  
  &__body {
    &--loading { opacity: 0.7; }
  }
  
  &--compact {
    padding: 8px;
  }
}
```

### 响应式设计
```less
// 使用Ant Design的响应式断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 组件响应式样式
.component-name {
  // 默认样式（移动端优先）
  padding: 8px;
  
  // 平板及以上
  @media (min-width: @screen-md) {
    padding: 16px;
  }
  
  // 桌面及以上
  @media (min-width: @screen-lg) {
    padding: 24px;
  }
}
```

## 性能优化规范

### React.memo使用
```typescript
// 对于纯展示组件使用React.memo
const DisplayComponent = React.memo<DisplayProps>(({ data, title }) => {
  return (
    <div>
      <h3>{title}</h3>
      <div>{data}</div>
    </div>
  );
});

// 自定义比较函数
const ComplexComponent = React.memo<ComplexProps>(
  ({ items, config }) => {
    return <div>{/* 复杂渲染逻辑 */}</div>;
  },
  (prevProps, nextProps) => {
    return prevProps.items.length === nextProps.items.length &&
           prevProps.config.id === nextProps.config.id;
  }
);
```

### 事件处理优化
```typescript
// 使用useCallback缓存事件处理函数
const FormComponent: React.FC<FormProps> = ({ onSubmit, data }) => {
  const handleSubmit = useCallback(
    (values: any) => {
      onSubmit?.(values);
    },
    [onSubmit]
  );
  
  const handleReset = useCallback(() => {
    // 重置逻辑
  }, []);
  
  return (
    <ProForm onFinish={handleSubmit} onReset={handleReset}>
      {/* 表单内容 */}
    </ProForm>
  );
};
```

## 测试规范

### 单元测试
```typescript
// 使用Jest + React Testing Library
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CreateForm from '../CreateForm';

describe('CreateForm', () => {
  it('应该正确渲染表单', () => {
    render(
      <CreateForm
        visible={true}
        onVisibleChange={jest.fn()}
        onFinish={jest.fn()}
      />
    );
    
    expect(screen.getByText('新增')).toBeInTheDocument();
  });
  
  it('应该正确处理表单提交', async () => {
    const mockFinish = jest.fn().mockResolvedValue(undefined);
    
    render(
      <CreateForm
        visible={true}
        onVisibleChange={jest.fn()}
        onFinish={mockFinish}
      />
    );
    
    // 填写表单并提交
    fireEvent.click(screen.getByText('确定'));
    
    await waitFor(() => {
      expect(mockFinish).toHaveBeenCalled();
    });
  });
});
```

### 可访问性测试
```typescript
// 检查可访问性属性
it('应该具有正确的可访问性属性', () => {
  render(<Component />);
  
  const button = screen.getByRole('button', { name: '提交' });
  expect(button).toHaveAttribute('aria-label', '提交表单');
  
  const input = screen.getByLabelText('用户名');
  expect(input).toHaveAttribute('aria-required', 'true');
});
```

## 最佳实践总结

### 组件设计原则
1. **单一职责**：每个组件只负责一个明确的功能
2. **可复用性**：设计时考虑在不同场景下的复用
3. **可配置性**：通过props提供足够的自定义选项
4. **可扩展性**：预留扩展接口，方便未来功能增强

### 代码质量要求
1. **TypeScript严格模式**：所有组件必须有完整的类型定义
2. **Props验证**：使用TypeScript接口定义Props类型
3. **错误边界**：关键组件添加错误边界处理
4. **性能监控**：大型组件添加性能监控代码

### 文档要求
1. **注释完整**：复杂逻辑添加详细注释
2. **使用示例**：提供常见使用场景的示例代码
3. **API文档**：Props接口和方法的详细说明
4. **更新日志**：记录组件的版本变更历史
