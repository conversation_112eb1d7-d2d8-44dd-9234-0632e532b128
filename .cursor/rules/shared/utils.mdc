---
description:
globs:
alwaysApply: false
---
# 工具函数 - 规范与最佳实践

## 工具函数架构
### 目录结构
```
src/utils/
├── kfcApi.ts                    # KFC API工具函数
├── tencentApi.ts                # 腾讯地图API工具函数
└── (其他工具函数)

# 建议扩展结构
src/utils/
├── api/                         # API相关工具
│   ├── kfcApi.ts               # KFC API
│   ├── tencentApi.ts           # 腾讯API
│   └── request.ts              # 通用请求工具
├── data/                       # 数据处理工具
│   ├── excel.ts                # Excel处理
│   ├── format.ts               # 数据格式化
│   └── validate.ts             # 数据验证
├── ui/                         # UI相关工具
│   ├── message.ts              # 消息提示
│   ├── download.ts             # 文件下载
│   └── clipboard.ts            # 剪贴板操作
└── common/                     # 通用工具
    ├── date.ts                 # 日期处理
    ├── string.ts               # 字符串处理
    └── number.ts               # 数字处理
```

## 现有工具函数规范

### KFC API工具 (kfcApi.ts)
#### 核心功能
```typescript
// 主要函数
export function batchFetchStoreInfoFromKFC(
  storeCodes: string[], 
  enableRetry: boolean = false
): Promise<StoreInfoResult>;

export function fetchTelFromKFC(storeCode: string): Promise<string | null>;

export function cleanStoreCode(storeCode: string): string;

// 私有辅助函数
function generateSignature(params: Record<string, any>, path: string): string;
function sleep(ms: number): Promise<void>;
```

#### 设计规范
```typescript
// 接口定义
interface StoreInfo {
  tel: string;
  marketName: string;
  cityName: string;
  districtName: string;
}

interface StoreInfoResult {
  found: Record<string, StoreInfo>;
  notFound: string[];
}

// 常量配置
const KFC_API_CONFIG = {
  baseUrl: 'https://emap.kfc.com.cn',
  endpoint: '/emap/getStoreByCode',
  batchSize: 30,
  maxRetries: 3,
  retryDelay: 1000,
  requestDelay: 500,
};

// 错误处理
class KFCApiError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'KFCApiError';
  }
}
```

### 腾讯地图API工具 (tencentApi.ts)
#### 核心功能
```typescript
// 主要函数
export function batchSyncStoresToTencent(
  storesData: StoreData[],
  onProgress?: (progress: ProgressInfo) => void
): Promise<SyncResult>;

export function syncSingleStoreToTencent(storeData: StoreData): Promise<boolean>;

export function generateUniqueId(): string;

// 类型定义
interface StoreData {
  name: string;
  longitude: number;
  latitude: number;
  address: string;
  storeCodeId?: string;
  kfc_raw_id?: string;
}

interface ProgressInfo {
  completed: number;
  total: number;
  currentStore: string;
}
```

#### 设计规范
```typescript
// API配置
const TENCENT_API_CONFIG = {
  url: 'https://locate.yumchina.com/dt/kfc/restapi/lbsproxy',
  requestDelay: 200,
  timeout: 10000,
  retryTimes: 2,
};

// 请求数据结构
interface TencentApiPayload {
  mapKind: string;
  reqKind: string;
  reqMethod: string;
  reqParam: string; // JSON字符串
}

interface TencentStoreData {
  dataid: string;
  source: string;
  type: number;
  status: number;
  data: {
    gas_data: string;
    name: string;
    lng: number;
    lat: number;
    addr: string;
  };
}
```

## 工具函数开发规范

### 函数设计原则
```typescript
// 1. 单一职责原则
// ✅ 好的例子
function cleanStoreCode(storeCode: string): string {
  const underscoreIndex = storeCode.indexOf('_');
  return underscoreIndex !== -1 ? storeCode.substring(0, underscoreIndex) : storeCode;
}

// ❌ 不好的例子
function processStoreCode(storeCode: string): { cleaned: string; isValid: boolean; tel?: string } {
  // 功能过多，违反单一职责
}

// 2. 纯函数优先
// ✅ 好的例子
function formatDate(date: Date, format: string): string {
  // 不修改输入参数，总是返回相同的结果
}

// ❌ 不好的例子
function updateGlobalConfig(newConfig: any): void {
  // 修改全局状态，有副作用
}
```

### 函数命名规范
```typescript
// 动词开头，语义明确
export function validateEmail(email: string): boolean;
export function formatCurrency(amount: number): string;
export function parseExcelData(file: File): Promise<any[]>;
export function generateUniqueId(): string;

// 异步函数使用async前缀或明确表达异步操作
export async function fetchStoreInfo(storeCode: string): Promise<StoreInfo>;
export function downloadExcelAsync(data: any[], filename: string): Promise<void>;

// 布尔函数使用is/has/can前缀
export function isValidStoreCode(storeCode: string): boolean;
export function hasRequiredFields(data: any): boolean;
export function canPerformOperation(user: User): boolean;
```

### 参数和返回值规范
```typescript
// 1. 使用接口定义复杂参数
interface FetchOptions {
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
}

export function fetchWithOptions(url: string, options: FetchOptions = {}): Promise<Response>;

// 2. 可选参数放在最后，提供默认值
export function formatNumber(
  value: number,
  decimals: number = 2,
  currency: string = 'CNY'
): string;

// 3. 统一错误处理方式
interface OperationResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

export async function safeOperation<T>(operation: () => Promise<T>): Promise<OperationResult<T>>;
```

### TypeScript类型定义
```typescript
// 1. 泛型函数
export function arrayToMap<T, K extends keyof T>(
  array: T[],
  keyField: K
): Map<T[K], T> {
  return new Map(array.map(item => [item[keyField], item]));
}

// 2. 联合类型
type SupportedFormat = 'xlsx' | 'csv' | 'json';
export function exportData(data: any[], format: SupportedFormat): Promise<Blob>;

// 3. 条件类型
type ApiResponse<T> = T extends string ? string : T extends number ? number : any;
export function processApiResponse<T>(response: T): ApiResponse<T>;

// 4. 工具类型
export function pickFields<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>;
export function omitFields<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>;
```

## 推荐的通用工具函数

### 数据验证工具
```typescript
// src/utils/data/validate.ts
export const Validators = {
  email: (email: string): boolean => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
  
  phone: (phone: string): boolean => /^1[3-9]\d{9}$/.test(phone),
  
  storeCode: (code: string): boolean => /^[A-Z0-9_]{3,20}$/.test(code),
  
  required: (value: any): boolean => value !== null && value !== undefined && value !== '',
  
  length: (value: string, min: number, max: number): boolean => 
    value.length >= min && value.length <= max,
    
  numeric: (value: string): boolean => /^\d+(\.\d+)?$/.test(value),
  
  url: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
};

// 组合验证器
export function createValidator<T>(rules: Record<keyof T, (value: any) => boolean>) {
  return (data: T): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    for (const [field, validator] of Object.entries(rules)) {
      if (!validator(data[field as keyof T])) {
        errors.push(`${field} validation failed`);
      }
    }
    
    return { valid: errors.length === 0, errors };
  };
}
```

### 数据格式化工具
```typescript
// src/utils/data/format.ts
export const Formatters = {
  // 日期格式化
  date: (date: Date | string, format: string = 'YYYY-MM-DD'): string => {
    const d = typeof date === 'string' ? new Date(date) : date;
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  },
  
  // 货币格式化
  currency: (amount: number, currency: string = 'CNY'): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency,
    }).format(amount);
  },
  
  // 文件大小格式化
  fileSize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  },
  
  // 手机号脱敏
  maskPhone: (phone: string): string => {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  },
  
  // 邮箱脱敏
  maskEmail: (email: string): string => {
    const [name, domain] = email.split('@');
    const maskedName = name.length > 2 
      ? name[0] + '*'.repeat(name.length - 2) + name[name.length - 1]
      : name;
    return `${maskedName}@${domain}`;
  },
};
```

### 文件操作工具
```typescript
// src/utils/ui/download.ts
export const FileUtils = {
  // 下载文本文件
  downloadText: (content: string, filename: string, mimeType: string = 'text/plain'): void => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  },
  
  // 下载Excel文件
  downloadExcel: (data: any[], filename: string): void => {
    import('xlsx').then(XLSX => {
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
      XLSX.writeFile(workbook, filename);
    });
  },
  
  // 读取文件内容
  readFileAsText: (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(reader.error);
      reader.readAsText(file);
    });
  },
  
  // 压缩图片
  compressImage: (file: File, quality: number = 0.8): Promise<Blob> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        canvas.toBlob(resolve, 'image/jpeg', quality);
      };
      
      img.src = URL.createObjectURL(file);
    });
  },
};
```

### 异步操作工具
```typescript
// src/utils/common/async.ts
export const AsyncUtils = {
  // 延迟函数
  sleep: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  
  // 超时包装
  withTimeout: <T>(promise: Promise<T>, timeout: number): Promise<T> => {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Operation timeout')), timeout)
      ),
    ]);
  },
  
  // 重试机制
  retry: async <T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxAttempts) {
          throw error;
        }
        await AsyncUtils.sleep(delay * attempt);
      }
    }
    throw new Error('Max retry attempts reached');
  },
  
  // 并发控制
  concurrent: async <T>(
    tasks: (() => Promise<T>)[],
    concurrency: number = 3
  ): Promise<T[]> => {
    const results: T[] = [];
    const executing: Promise<void>[] = [];
    
    for (const task of tasks) {
      const promise = task().then(result => {
        results.push(result);
      });
      
      executing.push(promise);
      
      if (executing.length >= concurrency) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }
    
    await Promise.all(executing);
    return results;
  },
  
  // 防抖函数
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void => {
    let timeoutId: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  },
  
  // 节流函数
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void => {
    let lastCall = 0;
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  },
};
```

## 工具函数测试规范

### 单元测试
```typescript
// src/utils/__tests__/validate.test.ts
import { Validators } from '../data/validate';

describe('Validators', () => {
  describe('email', () => {
    it('应该验证有效邮箱', () => {
      expect(Validators.email('<EMAIL>')).toBe(true);
      expect(Validators.email('<EMAIL>')).toBe(true);
    });
    
    it('应该拒绝无效邮箱', () => {
      expect(Validators.email('invalid-email')).toBe(false);
      expect(Validators.email('test@')).toBe(false);
      expect(Validators.email('@domain.com')).toBe(false);
    });
  });
  
  describe('storeCode', () => {
    it('应该验证有效门店代码', () => {
      expect(Validators.storeCode('ABC123')).toBe(true);
      expect(Validators.storeCode('XYZ_001')).toBe(true);
    });
    
    it('应该拒绝无效门店代码', () => {
      expect(Validators.storeCode('ab')).toBe(false);
      expect(Validators.storeCode('123456789012345678901')).toBe(false);
    });
  });
});
```

### 集成测试
```typescript
// src/utils/__tests__/kfcApi.integration.test.ts
import { batchFetchStoreInfoFromKFC } from '../kfcApi';

describe('KFC API Integration', () => {
  it('应该能够获取门店信息', async () => {
    const storeCodes = ['TEST001', 'TEST002'];
    const result = await batchFetchStoreInfoFromKFC(storeCodes);
    
    expect(result).toHaveProperty('found');
    expect(result).toHaveProperty('notFound');
    expect(typeof result.found).toBe('object');
    expect(Array.isArray(result.notFound)).toBe(true);
  }, 10000); // 增加超时时间
});
```

## 最佳实践总结

### 性能优化
1. **缓存策略**：对于重复计算的结果进行缓存
2. **懒加载**：大型工具库使用动态导入
3. **批量处理**：避免频繁的小批量操作
4. **内存管理**：及时清理不再使用的资源

### 错误处理
1. **统一错误类型**：定义标准的错误类和错误码
2. **错误恢复**：提供降级方案和重试机制
3. **用户友好**：提供清晰的错误信息
4. **日志记录**：记录详细的错误上下文

### 可维护性
1. **文档完善**：每个函数都有详细的JSDoc注释
2. **类型安全**：使用TypeScript严格类型检查
3. **测试覆盖**：确保关键函数有充分的测试
4. **版本管理**：记录API变更和兼容性信息

### 安全考虑
1. **输入验证**：所有外部输入都需要验证
2. **数据清理**：防止XSS和注入攻击
3. **敏感信息**：避免在日志中输出敏感数据
4. **权限检查**：确保操作权限验证
