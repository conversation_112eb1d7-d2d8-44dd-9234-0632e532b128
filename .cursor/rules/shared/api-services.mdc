---
description: 
globs: 
alwaysApply: false
---
# API服务层 - 通用规范与最佳实践

## 架构设计
### 服务层结构
```
src/services/
├── ant-design-pro/           # 主要业务API
│   ├── address.ts           # 地址管理API
│   ├── users.ts             # 用户管理API
│   ├── feedback.ts          # AI反馈API
│   ├── login.ts             # 登录认证API
│   ├── api.ts               # 通用API接口
│   ├── typings.d.ts         # API类型定义
│   └── index.ts             # API服务导出
└── swagger/                 # Swagger自动生成
```

## API调用规范

### 通用返回格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errorCode?: string;
  total?: number;  // 分页查询时使用
}
```

### 错误处理标准
```typescript
// 统一错误处理函数
const handleApiError = (error: any, defaultMessage: string) => {
  console.error('API调用失败:', error);
  message.error(error.message || defaultMessage);
  return { success: false, data: null };
};
```

### 分页查询参数
```typescript
interface PaginationParams {
  current?: number;      // 当前页码，默认1
  pageSize?: number;     // 每页条数，默认20
  [key: string]: any;    // 其他查询条件
}
```

## 具体API规范

### Supabase API调用
```typescript
// 推荐的调用模式
export async function queryData(params?: PaginationParams) {
  try {
    const { data, error, count } = await supabaseService.getData(params);
    
    if (error) {
      throw new Error(error.message);
    }
    
    return {
      success: true,
      data: data || [],
      total: count || 0,
    };
  } catch (error) {
    return handleApiError(error, '数据获取失败');
  }
}
```

### 外部API调用
```typescript
// KFC API调用示例
export async function callKfcApi(params: KfcApiParams) {
  try {
    const response = await request('/api/kfc/endpoint', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 特殊认证头
      },
      data: params,
    });
    
    return { success: true, data: response };
  } catch (error) {
    return handleApiError(error, 'KFC API调用失败');
  }
}
```

### 签名认证API
```typescript
// AI反馈API签名示例
export async function callSignedApi(params: any, isProduction = true) {
  const baseUrl = isProduction 
    ? 'https://aiordering.kfc.com.cn'
    : 'https://tappagent.kfc.com.cn';
    
  const signature = generateSignature(params);
  
  return request(`${baseUrl}/endpoint`, {
    method: 'POST',
    headers: {
      'kbcts': signature.timestamp,
      'kbsv': signature.hash,
      'Content-Type': 'application/json',
    },
    data: params,
  });
}
```

## 类型定义规范

### 接口定义
```typescript
// 基础接口定义
export interface BaseRecord {
  id?: string;
  created_at?: string;
  updated_at?: string;
}

// 具体业务接口继承基础接口
export interface UserRecord extends BaseRecord {
  user_id: string;
  name: string;
  email: string;
  ant_design_role: 'admin' | 'user';
  group?: string;
}
```

### 请求参数类型
```typescript
// CRUD操作参数类型
export interface CreateUserParams {
  email: string;
  password: string;
  name: string;
  group: string;
  ant_design_role: 'admin' | 'user';
}

export interface UpdateUserParams {
  name?: string;
  group?: string;
  ant_design_role?: 'admin' | 'user';
  phone?: string;
}
```

## 最佳实践

### 错误处理
1. **统一错误处理**：所有API调用使用统一的错误处理函数
2. **用户友好提示**：错误信息对用户友好，技术细节记录到控制台
3. **降级处理**：关键功能失败时提供备选方案
4. **重试机制**：网络请求失败时适当重试

### 性能优化
1. **请求去重**：避免短时间内重复请求
2. **缓存策略**：合理使用缓存减少网络请求
3. **分页加载**：大数据量使用分页查询
4. **并发控制**：限制并发请求数量

### 安全考虑
1. **参数验证**：前端参数验证，防止恶意输入
2. **敏感信息**：密钥等敏感信息不暴露在前端
3. **权限检查**：API调用前检查用户权限
4. **签名验证**：关键接口使用签名机制

### 测试建议
1. **单元测试**：为每个API函数编写单元测试
2. **集成测试**：测试API与UI组件的集成
3. **错误场景**：测试网络错误、权限错误等异常场景
4. **性能测试**：测试大数据量场景的性能表现

## 常见问题和解决方案

### 问题1：请求参数污染
**场景**：全局拦截器添加的参数影响特定API
**解决**：为特定API添加例外处理逻辑

### 问题2：TypeScript类型错误
**场景**：API返回数据与类型定义不匹配
**解决**：使用类型断言或运行时类型检查

### 问题3：并发请求冲突
**场景**：用户快速操作触发多个相同请求
**解决**：使用防抖或请求去重机制

### 问题4：大数据量导出超时
**场景**：一次性获取大量数据导致请求超时
**解决**：分批次获取，使用分页机制

## 代码审查清单

- [ ] API函数有完整的TypeScript类型定义
- [ ] 错误处理覆盖所有可能的异常情况
- [ ] 返回格式符合统一的ApiResponse接口
- [ ] 敏感信息（密钥、密码）不在前端暴露
- [ ] 大数据量查询使用分页机制
- [ ] 关键操作有适当的权限检查
- [ ] API调用有合理的超时设置
- [ ] 错误信息对用户友好且信息充分
