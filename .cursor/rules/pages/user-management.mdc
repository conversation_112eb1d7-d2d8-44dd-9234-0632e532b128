---
description: 
globs: 
alwaysApply: false
---
# 用户管理页面 - 功能规范与迭代记录

## 页面概述
- **功能定位**: 管理员专用的用户账号管理系统
- **访问路径**: `/user-management`
- **权限要求**: 仅限admin角色用户访问
- **主要用户**: 系统管理员
- **数据源**: Supabase Authentication + user_profiles表

## 当前功能特性
### 核心功能
- [x] 用户列表展示（昵称、邮箱、用户组、角色、登录时间）
- [x] 新增用户（用户名自动添加@163.com后缀）
- [x] 编辑用户信息（昵称、组、角色、手机号）
- [x] 删除用户（禁止删除管理员用户）
- [x] 多条件搜索和筛选

### 权限控制
- 只有admin角色用户能访问此页面
- 不能删除任何管理员角色用户
- 自动禁用新增用户的自动填充功能

### 数据验证
- 用户名：3-20位字母数字下划线
- 必填字段：用户名、密码、昵称、用户组、角色
- 用户组：下拉选择（用户团队/管理团队）

## 技术实现架构
### 组件结构
```
UserManagement/
├── index.tsx                 # 主页面组件
├── components/
│   ├── CreateForm.tsx       # 新增用户表单
│   └── EditForm.tsx         # 编辑用户表单
```

### API接口
- `queryUsers()` - 获取用户列表（支持分页和搜索）
- `createUser()` - 创建新用户（Auth + Profile）
- `updateUserInfo()` - 更新用户信息
- `deleteUser()` - 删除用户（Auth + Profile）

### 数据流向
```
UI组件 → API Service → Supabase Service → Database
         ↓
    Error Handling → User Feedback
```

## 历史迭代记录

### v1.0.0 (需求21 - 2024年当前)
**新增功能**：
- 初始版本用户管理页面
- 用户CRUD基础功能
- 权限控制集成

### v1.1.0 (需求22 - 2024年当前)
**功能完善**：
- 修复编辑功能权限问题（改用supabaseAdmin）
- 用户组字段标准化（下拉选择）
- 优化新增用户数据验证

### v1.2.0 (需求23 - 2024年当前)
**功能修复和优化**：
- 修复删除功能数据ID问题
- 简化编辑表单（移除个性签名、职位、地址字段）
- 用户名输入方式调整（自动添加@163.com后缀）
- 安全优化：禁用表单自动填充
- 删除权限调整：只允许删除普通用户

## 已解决的技术问题

### 问题1：数据库字段错误 (v1.2.0修复)
**问题**: `user_profiles`表主键是`user_id`而非`id`
**解决**: 修改所有数据库操作，统一使用`user_id`作为主键

### 问题2：权限不足错误 (v1.1.0修复)
**问题**: 普通用户权限无法更新`user_profiles`表
**解决**: 改用`supabaseAdmin`客户端进行用户管理操作

### 问题3：表单自动填充安全风险 (v1.2.0修复)
**问题**: 浏览器自动填充可能泄露敏感信息
**解决**: 添加`autoComplete`属性禁用自动填充

## 当前技术限制
- 用户密码创建后无法修改（需要单独实现）
- 批量操作功能待开发
- 用户活动日志记录待完善

## 开发规范
### 代码约定
- 使用ProTable组件实现表格功能
- API调用统一使用async/await模式
- 错误处理提供用户友好提示
- 表单验证使用ProForm内置规则

### 安全要求
- 所有用户管理操作需要admin权限验证
- 敏感操作需要二次确认
- 用户密码不能在前端明文传输
- 删除操作需要检查用户角色

### 测试建议
- 测试admin和user角色的访问权限
- 测试用户CRUD操作的数据完整性
- 测试表单验证的各种边界情况
- 测试删除保护机制的有效性
