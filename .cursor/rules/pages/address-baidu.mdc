---
description: 
globs: 
alwaysApply: false
---
# 百度地址管理页面 - 功能规范与迭代记录

## 页面概述
- **功能定位**: KFC门店地址信息管理和维护系统
- **访问路径**: `/address/baidu`
- **权限要求**: 仅限admin角色用户访问
- **主要用户**: 系统管理员、数据管理员
- **数据源**: Supabase address_baidu表

## 当前功能特性
### 核心功能
- [x] 门店地址列表展示（门店ID、名称、地址、城市等）
- [x] Excel批量导入功能（支持KFC API信息补充）
- [x] 多格式Excel导出（百度格式+腾讯格式）
- [x] 多选和批量操作（删除选中、导出选中）
- [x] 高级搜索（名称、门店ID、地址、时间范围）
- [x] CRUD操作（新增、编辑、删除门店信息）

### 数据展示列
| 字段 | 显示名称 | 默认显示 | 特殊功能 |
|------|----------|----------|----------|
| storeCodeId | 门店ID | ✓ | - |
| name | 名称 | ✓ | - |
| address | 地址 | ✓ | - |
| cityName | 城市 | ✓ | - |
| tel | 电话 | ✓ | KFC API自动获取 |
| longitude | 经度 | ✓ | - |
| latitude | 纬度 | ✓ | - |
| updated_at | 更新时间 | ✓ | 降序排序 |
| storeCodePoiid | 子点poiid | ✗ | 隐藏列 |
| marketName | 市场 | ✗ | 隐藏列 |
| parentName | 主点名称 | ✗ | 隐藏列 |

### Excel导入功能
- **字段映射**：
  - '子点cpid(门店id)' → 'storeCodeId'
  - '子点名称' → 'name'
  - '子点地址' → 'address'
  - '城市' → 'cityName'
  - '子点x' → 'longitude'
  - '子点y' → 'latitude'
- **KFC API集成**：自动获取门店电话和省市区信息
- **数据处理**：门店ID清理（去除_xx后缀）、省市区信息拼接
- **批量处理**：每批30条数据，支持重试机制

### Excel导出功能
- **百度格式**：`kfc_address_for_baidu_YYYY_MM_DD_HH_MM_SS.xlsx`
- **腾讯格式**：`kfc_address_for_tencent_YYYY_MM_DD_HH_MM_SS.xlsx`
- **导出模式**：全量导出、选中导出
- **字段优化**：必填/非必填字段标注，列宽自动调整

## 技术实现架构
### 组件结构
```
Address/
├── index.tsx                    # 主页面组件
├── index.css                   # 页面样式
├── components/
│   ├── CreateForm.tsx          # 新增地址表单
│   ├── Editor.tsx              # 编辑地址表单
│   └── UploadProgress.tsx      # 导入进度显示
```

### API接口
- `queryAddress()` - 获取地址列表（支持分页和搜索）
- `addAddress()` - 新增地址信息
- `updateAddress()` - 更新地址信息
- `removeAddress()` - 删除地址信息
- `batchFetchStoreInfoFromKFC()` - 批量获取KFC门店信息

### 外部API集成
```typescript
// KFC API配置
const KFC_API = {
  baseUrl: 'https://emap.kfc.com.cn',
  endpoint: '/emap/getStoreByCode',
  auth: {
    client_key: 'xxx',
    client_sec: 'xxx'
  }
}
```

### 数据流向
```
Excel导入 → 数据解析 → KFC API补充 → 省市区处理 → Supabase存储
UI操作 → 表单验证 → API调用 → 数据库更新 → 界面刷新
```

## 历史迭代记录

### v1.0.0 (需求4 - 2024-05-03)
**新增功能**：
- 初始版本地址管理页面
- 基础CRUD功能和mock数据
- ProTable表格展示和搜索

### v2.0.0 (需求1 - 2024-05-03)
**Supabase集成**：
- 接入Supabase数据服务
- 替换mock数据为真实数据库操作
- 完善错误处理机制

### v3.0.0 (需求5 - 2024-06-07)
**Excel导入导出**：
- 实现Excel文件导入功能
- 字段映射和数据类型转换
- 双格式导出（百度+腾讯）

### v3.1.0 (需求6 - 2024年当前)
**KFC API集成**：
- 集成KFC门店信息API
- 自动获取电话信息
- 批量处理和错误重试

### v3.2.0 (需求9 - 2024年当前)
**省市区信息处理**：
- 自动添加省市区信息到地址
- 智能处理直辖市重复问题
- 优化地址显示格式

### v4.0.0 (需求7 - 2024年当前)
**多选和批量操作**：
- 增加表格行选择功能
- 批量删除和导出选中
- 按钮状态控制优化

### v4.1.0 (需求8 - 2024年当前)
**导入导出优化**：
- 分批处理大数据量导入
- 导出当前查询条件数据
- 进度反馈和用户体验提升

### v5.0.0 (需求15 - 2024年当前)
**精美进度条**：
- 替换toast为精美进度条UI
- 数据录入进度详细展示
- KFC API处理进度实时反馈

### v6.0.0 (需求16 - 2024年当前)
**功能拆分优化**：
- 移除腾讯同步相关功能
- 简化为纯百度地址管理
- 优化进度显示为单阶段

## 已解决的技术问题

### 问题1：门店ID后缀处理 (v3.2.0修复)
**问题**：门店ID包含`_D1`等后缀影响API调用
**解决**：实现`cleanStoreCode`函数去除后缀

### 问题2：API批次大小优化 (v3.1.0修复)
**问题**：100条批次导致API超时
**解决**：调整为30条批次，提高成功率

### 问题3：重试机制实现 (v3.1.0修复)
**问题**：部分门店信息获取失败
**解决**：实现最多3次重试机制

### 问题4：省市区重复显示 (v3.2.0修复)
**问题**：直辖市出现"北京北京"重复
**解决**：智能检测省市名称相同时只保留市名

### 问题5：按钮状态混淆 (v4.0.0修复)
**问题**：选中行时主按钮依然可用
**解决**：选中时禁用主导入导出按钮

## 当前技术限制
- KFC API有请求频率限制，需要控制并发数
- Excel导入大文件可能内存占用较高
- 省市区信息依赖KFC API，API不可用时功能受限
- 批量操作时间较长，用户需要等待

## 开发规范
### 数据处理规范
- 门店ID统一使用`cleanStoreCode`函数处理
- 省市区信息拼接检查重复
- Excel字段映射严格按照既定规则
- 时间字段使用ISO格式存储

### API调用约定
- KFC API调用使用批量模式，每批最多30条
- 失败重试最多3次，间隔递增
- 请求签名使用MD5算法
- 错误处理提供详细日志

### 用户体验要求
- 大数据量操作提供进度反馈
- 批量操作支持取消功能
- 错误信息友好且具体
- 成功操作提供明确提示

### 性能优化建议
- 表格数据使用分页加载
- Excel处理使用Web Workers（可考虑）
- 图片和大文件使用懒加载
- API请求结果适当缓存

### 安全要求
- 上传文件大小限制（3MB）
- 文件类型验证（仅Excel）
- 用户权限验证（admin only）
- 敏感API密钥不暴露在前端
