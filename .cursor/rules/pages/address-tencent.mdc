---
description: 
globs: 
alwaysApply: false
---
# 腾讯地址管理页面 - 功能规范与迭代记录

## 页面概述
- **功能定位**: 腾讯地图服务门店信息同步管理系统
- **访问路径**: `/address/tencent`
- **权限要求**: 仅限admin角色用户访问
- **主要用户**: 系统管理员、地图数据管理员
- **数据源**: Supabase address_tencent表

## 当前功能特性
### 核心功能
- [x] 门店地址列表展示和基础CRUD操作
- [x] Excel导入功能（简化流程，无KFC API调用）
- [x] 腾讯地图API同步功能
- [x] 单一腾讯格式Excel导出
- [x] 新增/编辑时自动同步到腾讯地图
- [x] 多选和批量操作

### 数据展示列
| 字段 | 显示名称 | 默认显示 | 特殊功能 |
|------|----------|----------|----------|
| storeCodeId | 门店ID | ✓ | - |
| name | 名称 | ✓ | - |
| address | 地址 | ✓ | - |
| cityName | 城市 | ✓ | - |
| tel | 电话 | ✓ | - |
| longitude | 经度 | ✓ | - |
| latitude | 纬度 | ✓ | - |
| updated_at | 更新时间 | ✓ | 降序排序 |

### Excel导入功能
- **简化流程**：直接导入数据，无外部API调用
- **腾讯同步**：导入完成后自动同步到腾讯地图服务
- **字段映射**：与百度地址管理相同的字段映射规则
- **批量处理**：数据导入30条/批，腾讯同步逐条处理

### 腾讯地图API同步
- **API配置**：
  ```typescript
  {
    url: 'https://locate.yumchina.com/dt/kfc/restapi/lbsproxy',
    method: 'POST',
    format: 'FormData'
  }
  ```
- **数据格式**：
  ```typescript
  {
    mapKind: '2',
    reqKind: '1', 
    reqMethod: '/ws/native/lbsserver/api/lbsdata/create',
    reqParam: JSON.stringify({
      dataid: uniqueId,
      source: 'kfc_chesuqu_offline',
      type: 1,
      status: 2,
      data: {
        gas_data: 'my test data',
        name: storeName,
        lng: longitude,
        lat: latitude,
        addr: address
      }
    })
  }
  ```

### Excel导出功能
- **腾讯格式**：`kfc_address_for_tencent_YYYY_MM_DD_HH_MM_SS.xlsx`
- **字段映射**：
  - marketName → 市场
  - storeCodeId → 子点cpid(门店id)
  - storeCodePoiid → 子点poiid
  - cityName → 城市
  - name → 子点名称
  - address → 子点地址
  - longitude → 子点x
  - latitude → 子点y
  - parentStoreCodePoiid → 主点poiid
  - parentName → 主点名称
  - "已上线" → 备注（固定值）
  - parentStoreCodeId → 母店号

## 技术实现架构
### 组件结构
```
AddressTencent/
├── index.tsx                    # 主页面组件
├── components/
│   ├── CreateForm.tsx          # 新增地址表单
│   ├── Editor.tsx              # 编辑地址表单
│   └── UploadProgress.tsx      # 导入进度显示
```

### API接口
- `queryTencentAddress()` - 获取腾讯地址列表
- `addTencentAddress()` - 新增地址信息
- `updateTencentAddress()` - 更新地址信息
- `removeTencentAddress()` - 删除地址信息
- `syncSingleStoreToTencent()` - 单个门店同步到腾讯
- `batchSyncStoresToTencent()` - 批量门店同步到腾讯

### 腾讯API集成
```typescript
// 腾讯API工具函数
const tencentApi = {
  syncSingleStore: (storeData) => FormData请求,
  generateUniqueId: () => timestamp + randomString,
  batchSync: (storesArray) => 逐个调用syncSingleStore
}
```

### 数据流向
```
Excel导入 → 数据解析 → Supabase存储 → 腾讯API同步
CRUD操作 → 数据验证 → Supabase更新 → 腾讯API同步
```

## 历史迭代记录

### v1.0.0 (需求16 - 2024年当前)
**功能拆分**：
- 从百度地址管理页面拆分独立
- 创建独立的腾讯地址数据表
- 实现基础CRUD功能

### v1.1.0 (需求14 - 2024年当前)
**腾讯API集成**：
- 集成腾讯地图API同步功能
- Excel导入后自动同步到腾讯
- 实现批量同步机制

### v1.2.0 (需求15 - 2024年当前)
**进度条优化**：
- 实现双进度条显示（数据导入+腾讯同步）
- 精美的进度UI设计
- 详细的批次处理反馈

### v1.3.0 (需求16后续优化 - 2024年当前)
**流程简化**：
- 移除KFC API调用逻辑
- 简化Excel导入流程
- 专注腾讯同步功能

### v1.4.0 (需求17 - 2024年当前)
**API调用优化**：
- 批量同步改为逐个调用
- data参数从数组改为单个对象
- 减少单次请求延迟到200ms

### v1.5.0 (进度条合并 - 2024年当前)
**UI优化**：
- 双进度条合并为单一进度条
- 权重分配：数据导入60% + 腾讯同步40%
- 统一样式风格

### v1.6.0 (新增编辑同步 - 2024年当前)
**功能增强**：
- 新增地址时自动同步到腾讯
- 编辑地址时自动同步到腾讯
- 完善的两阶段处理和错误提示

## 已解决的技术问题

### 问题1：腾讯API调用方式优化 (v1.4.0修复)
**问题**：批量调用时data参数为数组，接口要求单个对象
**解决**：改为逐个调用，每次传递单个门店对象

### 问题2：FormData格式问题 (v1.1.0修复)
**问题**：初始使用JSON格式发送请求失败
**解决**：改用FormData格式，移除Content-Type头

### 问题3：进度显示复杂性 (v1.5.0修复)
**问题**：双进度条UI复杂，用户理解困难
**解决**：合并为单一进度条，内部合理分配权重

### 问题4：数据完整性检查 (v1.6.0修复)
**问题**：部分门店缺少必要字段导致同步失败
**解决**：添加必要字段检查，提供友好提示

## 当前技术限制
- 腾讯API没有批量接口，只能逐个同步
- 腾讯同步失败时无法回滚数据库操作
- 大批量数据同步时间较长
- 腾讯API响应格式文档不完整

## 历史迭代记录

### v1.0.0 (需求16 - 2024年当前)
**新增功能**：
- 从原地址管理页面拆分出腾讯专用管理页面
- 使用独立数据表address_tencent
- 专注腾讯地图服务集成功能

### v1.1.0 (功能简化优化)
**功能调整**：
- 移除KFC API调用，简化导入流程
- 优化进度条显示为单一进度条
- 删除门店信息自动获取功能

### v1.2.0 (腾讯同步优化)
**技术改进**：
- 新增/编辑时支持腾讯API同步
- 优化单个门店同步逻辑
- 完善错误处理和用户反馈

### v1.3.0 (API调用方式重构)
**重要变更**：
- 腾讯API从批量调用改为逐个调用
- data参数从数组改为单个对象
- 优化进度显示和错误处理

## 已解决的技术问题

### 问题1：进度条复杂度过高 (v1.1.0修复)
**问题描述**：双进度条显示复杂，用户体验不佳
**解决方案**：合并为单一进度条，权重分配60%数据导入+40%腾讯同步

### 问题2：API调用方式优化 (v1.3.0修复)
**问题描述**：批量API调用方式不符合接口设计
**解决方案**：改为逐个门店调用，data参数使用单个对象格式

### 问题3：功能边界不清晰 (v1.0.0解决)
**问题描述**：与百度地址管理功能重叠混淆
**解决方案**：拆分为独立页面，使用独立数据表，明确功能职责

## 开发规范
### 腾讯API调用约定
- 使用FormData格式发送请求
- 每次只同步一个门店信息
- 请求间隔200ms，避免频率限制
- 生成唯一dataid避免重复

### 数据同步策略
- 数据库操作优先，同步失败不影响数据保存
- 提供明确的同步状态反馈
- 区分数据保存成功和同步成功的不同状态
- 失败时提供重试建议

### 错误处理规范
- **数据保存失败**：显示具体错误，阻止后续同步
- **同步失败**：数据已保存，提示同步失败可稍后重试
- **数据不完整**：跳过同步，提示用户补充必要信息
- **网络错误**：提供重试机制和明确的错误信息

### 用户体验要求
- 大批量操作提供取消功能
- 进度显示具体的门店名称和序号
- 成功后显示详细的统计信息
- 失败时提供具体的解决建议

### 性能优化建议
- 适当控制并发同步数量
- 对同步结果进行缓存
- 避免重复同步相同数据
- 提供离线模式支持（可考虑）

### 安全要求
- 腾讯API密钥信息不暴露
- 门店数据脱敏处理
- 同步日志记录便于审计
- 权限验证确保只有管理员可操作
