---
description: 
globs: 
alwaysApply: false
---
# AI点餐反馈统计页面 - 功能规范与迭代记录

## 页面概述
- **功能定位**: APP功能反馈数据统计分析系统
- **访问路径**: `/ai-feedback`
- **权限要求**: admin和user角色均可访问
- **主要用户**: 系统管理员、数据分析员
- **数据源**: KFC AI点餐服务反馈接口

## 当前功能特性
### 核心功能
- [x] 反馈数据列表展示（时间、ID、手机号、问题类别等）
- [x] 多条件搜索（手机号、时间范围）
- [x] 环境切换（生产/测试环境）
- [x] 数据导出Excel功能
- [x] 图片预览和视频链接处理
- [x] 手机号和Session ID复制功能

### 数据展示列
| 字段 | 显示名称 | 特殊功能 |
|------|----------|----------|
| feedback_time | 反馈时间 | 默认降序排序 |
| id | ID | - |
| mobile | 手机号 | 支持复制 |
| session_id | Session ID | 支持复制 |
| problem_category | 问题类别 | Tag标签显示 |
| feedback_content | 反馈内容 | Tooltip+复制功能 |
| image_list | 图片列表 | 缩略图预览 |
| video | 视频 | 外部链接 |

### 搜索功能
- **手机号搜索**: 精确匹配
- **时间范围搜索**: 起始时间到结束时间
- **环境切换**: 生产/测试API端点切换

### 导出功能
- **全量导出**: 基于当前搜索条件导出所有数据
- **选中导出**: 导出用户选中的记录
- **分页获取**: 每页20条数据逐步写入Excel
- **文件命名**: `ai_feedback_YYYY_MM_DD_HH_MM_SS.xlsx`

## 技术实现架构
### API配置
```typescript
// 生产环境
baseUrl: 'https://aiordering.kfc.com.cn'

// 测试环境  
baseUrl: 'https://tappagent.kfc.com.cn'

// 接口路径
endpoint: '/agent-service/aiagent/feedback/query'
```

### 签名认证机制
```typescript
// 请求头签名
const signature = {
  kbcts: timestamp,
  kbsv: md5Hash,
  'Content-Type': 'application/json'
}

// 签名字符串
signString = `${client_key}\t${client_sec}\t${kbcts}\t${signPath}\t${JSON.stringify(params)}`
```

### 必需API参数
```typescript
const requiredParams = {
  business: 'preorder',
  brand: 'KFC_PRE', 
  agentService: 'ali',
  portalSource: 'aiordering',
  deviceId: '51055aac3d225643d7d314269adae023'
}
```

## 历史迭代记录

### v1.0.0 (需求18 - 2024年当前)
**新增功能**：
- 初始版本AI反馈统计页面
- 基础列表展示和搜索功能
- 特殊签名认证机制实现

### v1.1.0 (API调用方式修复)
**修复内容**：
- 修改GET请求为POST请求
- 调整签名逻辑适配POST参数
- 修复URL参数污染问题

### v1.2.0 (API参数完善)
**功能完善**：
- 补充必需的业务参数
- 修正签名路径计算
- 优化错误处理机制

### v1.3.0 (UI和功能优化)
**界面优化**：
- 反馈时间列位置调整到最前
- 默认降序排序
- 环境切换功能
- 搜索按钮布局优化
- 反馈内容列交互优化

### v1.4.0 (导出功能)
**新增功能**：
- Excel导出功能（全量/选中）
- 分页数据获取机制
- 时间戳格式化处理
- 多选行操作支持

## 已解决的技术问题

### 问题1：POST请求方式问题 (v1.1.0修复)
**问题**: 接口需要POST而非GET请求
**解决**: 修改请求方式，调整参数传递和签名计算

### 问题2：URL参数污染 (v1.1.0修复)
**问题**: 全局拦截器添加token参数影响API调用
**解决**: 为AI反馈API添加例外处理逻辑

### 问题3：签名路径错误 (v1.2.0修复)
**问题**: 签名计算使用完整路径而非相对路径
**解决**: 分离签名路径和完整API路径

### 问题4：ProTable valueType冲突 (v1.3.0修复)
**问题**: valueType与自定义render函数冲突
**解决**: 移除valueType配置，使用filters实现筛选

## 当前技术限制
- 不支持实时数据刷新（需手动刷新）
- 图片预览功能较基础（可考虑升级为画廊模式）
- 大数据量导出性能待优化
- 视频预览功能待完善

## 开发规范
### API调用约定
- 使用POST方式请求
- 严格按照签名机制构建请求头
- 环境切换通过baseUrl区分
- 错误处理提供友好提示

### 数据处理规范
- 时间戳统一转换为本地化格式
- JSON字段安全解析（图片列表）
- 长文本内容适当截断显示
- 空值/null值统一处理为"无"

### 性能优化建议
- 列表数据使用分页加载
- 图片使用懒加载或缩略图
- 导出功能添加进度提示
- 大数据量场景考虑虚拟滚动

### 安全要求
- API密钥信息不能暴露在前端
- 签名机制确保请求合法性
- 用户数据脱敏处理
- 错误信息不能泄露系统信息
