---
description: 项目指南：记录了整个项目的结构、项目的功能简介、技术规则及规范等
globs: 
alwaysApply: false
---
# KFC智能运营管理平台 - 项目结构指南

## 项目概述
这是一个基于Ant Design Pro的KFC智能运营管理平台，使用Supabase作为后端数据服务，主要包含地址管理、用户管理和AI点餐反馈统计等功能模块。

## 技术栈
- **前端框架**: React + TypeScript + UmiJS 4.x
- **UI组件库**: Ant Design Pro + ProComponents
- **后端服务**: Supabase (数据库 + 认证)
- **包管理器**: pnpm
- **代码规范**: ESLint + Prettier
- **测试框架**: Jest
- **构建工具**: UmiJS内置Webpack
- **状态管理**: UmiJS内置状态管理
- **国际化**: UmiJS内置i18n (支持8种语言)

## 核心目录结构

### 配置文件目录 (`config/`)
- [config.ts](mdc:config/config.ts) - 主配置文件，包含UmiJS配置和环境变量
- [routes.ts](mdc:config/routes.ts) - 路由配置，定义所有页面路由和权限
- [defaultSettings.ts](mdc:config/defaultSettings.ts) - 默认设置配置
- [proxy.ts](mdc:config/proxy.ts) - 代理配置
- [oneapi.json](mdc:config/oneapi.json) - API配置文件

### 源代码目录 (`src/`)

#### 页面组件 (`src/pages/`)
- [Welcome.tsx](mdc:src/pages/Welcome.tsx) - 欢迎页面
- [Address/](mdc:src/pages/Address) - 百度地址管理页面
- [AddressTencent/](mdc:src/pages/AddressTencent) - 腾讯地址管理页面
- [AiFeedback/](mdc:src/pages/AiFeedback) - AI点餐反馈统计页面
- [UserManagement/](mdc:src/pages/UserManagement) - 用户管理页面
- [User/Login/](mdc:src/pages/User/Login) - 登录页面
- [403.tsx](mdc:src/pages/403.tsx) - 权限不足页面
- [404.tsx](mdc:src/pages/404.tsx) - 页面未找到

#### API服务层 (`src/services/`)
- [ant-design-pro/](mdc:src/services/ant-design-pro) - 主要API服务
  - [address.ts](mdc:src/services/ant-design-pro/address.ts) - 地址管理API
  - [users.ts](mdc:src/services/ant-design-pro/users.ts) - 用户管理API
  - [feedback.ts](mdc:src/services/ant-design-pro/feedback.ts) - AI反馈API
  - [login.ts](mdc:src/services/ant-design-pro/login.ts) - 登录认证API
  - [api.ts](mdc:src/services/ant-design-pro/api.ts) - 通用API接口
  - [typings.d.ts](mdc:src/services/ant-design-pro/typings.d.ts) - API类型定义
  - [index.ts](mdc:src/services/ant-design-pro/index.ts) - API服务导出
- [swagger/](mdc:src/services/swagger) - Swagger自动生成的API服务

#### 工具函数 (`src/utils/`)
- [kfcApi.ts](mdc:src/utils/kfcApi.ts) - KFC门店信息API调用工具
- [tencentApi.ts](mdc:src/utils/tencentApi.ts) - 腾讯地图API同步工具

#### 全局配置和样式
- [app.tsx](mdc:src/app.tsx) - 应用入口配置，包含初始状态和权限控制
- [access.ts](mdc:src/access.ts) - 权限控制配置
- [global.tsx](mdc:src/global.tsx) - 全局初始化
- [global.less](mdc:src/global.less) - 全局样式文件
- [requestErrorConfig.ts](mdc:src/requestErrorConfig.ts) - 请求错误配置
- [typings.d.ts](mdc:src/typings.d.ts) - TypeScript类型声明
- [manifest.json](mdc:src/manifest.json) - PWA应用配置清单
- [service-worker.js](mdc:src/service-worker.js) - Service Worker服务

#### 全局组件 (`src/components/`)
- [Footer/](mdc:src/components/Footer) - 页脚组件
- [RightContent/](mdc:src/components/RightContent) - 右侧内容组件
- [HeaderDropdown/](mdc:src/components/HeaderDropdown) - 头部下拉组件
- [index.ts](mdc:src/components/index.ts) - 组件导出文件

#### 国际化 (`src/locales/`)
- [zh-CN/](mdc:src/locales/zh-CN) - 简体中文语言包
- [en-US/](mdc:src/locales/en-US) - 英语语言包
- [zh-TW/](mdc:src/locales/zh-TW) - 繁体中文语言包
- [ja-JP/](mdc:src/locales/ja-JP) - 日语语言包
- [pt-BR/](mdc:src/locales/pt-BR) - 葡萄牙语语言包
- [id-ID/](mdc:src/locales/id-ID) - 印尼语语言包
- [fa-IR/](mdc:src/locales/fa-IR) - 波斯语语言包
- [bn-BD/](mdc:src/locales/bn-BD) - 孟加拉语语言包

### Supabase数据服务 (`supabase/`)
- [index.ts](mdc:supabase/index.ts) - Supabase客户端初始化
- [address.ts](mdc:supabase/address.ts) - 百度地址数据服务
- [address_tencent.ts](mdc:supabase/address_tencent.ts) - 腾讯地址数据服务
- [users.ts](mdc:supabase/users.ts) - 用户管理数据服务
- [auth.ts](mdc:supabase/auth.ts) - 认证服务
- [env-check.ts](mdc:supabase/env-check.ts) - 环境变量检查
- [README.md](mdc:supabase/README.md) - Supabase服务说明文档
- [scripts/](mdc:supabase/scripts) - 数据库初始化和测试脚本

### Mock数据 (`mock/`)
- [address.ts](mdc:mock/address.ts) - 地址管理模拟数据
- [user.ts](mdc:mock/user.ts) - 用户管理模拟数据
- [notices.ts](mdc:mock/notices.ts) - 通知模拟数据
- [route.ts](mdc:mock/route.ts) - 路由模拟数据
- [requestRecord.mock.js](mdc:mock/requestRecord.mock.js) - 请求记录模拟数据

### 静态资源 (`public/`)
- [favicon.ico](mdc:public/favicon.ico) - 网站图标
- [logo.svg](mdc:public/logo.svg) - 主Logo
- [pro_icon.svg](mdc:public/pro_icon.svg) - Pro版本图标
- [icons/](mdc:public/icons) - PWA应用图标集合
- [scripts/](mdc:public/scripts) - 公共脚本文件

### 类型定义 (`types/`)
- [index.d.ts](mdc:types/index.d.ts) - 全局类型定义
- [cache/](mdc:types/cache) - 缓存相关类型定义

### 测试配置 (`tests/`)
- [setupTests.jsx](mdc:tests/setupTests.jsx) - 测试环境配置

### 项目配置文件
- [package.json](mdc:package.json) - 项目依赖和脚本配置
- [pnpm-lock.yaml](mdc:pnpm-lock.yaml) - pnpm包管理器锁定文件
- [tsconfig.json](mdc:tsconfig.json) - TypeScript编译配置
- [jsconfig.json](mdc:jsconfig.json) - JavaScript项目配置
- [jest.config.ts](mdc:jest.config.ts) - Jest测试框架配置
- [.eslintrc.js](mdc:.eslintrc.js) - ESLint代码检查配置
- [.prettierrc.js](mdc:.prettierrc.js) - Prettier代码格式化配置
- [.editorconfig](mdc:.editorconfig) - 编辑器配置
- [README.md](mdc:README.md) - 项目说明文档

## 功能模块结构

### 权限控制系统
- 基于用户角色的权限控制（admin/user）
- 管理员权限：可访问所有功能
- 普通用户权限：只能访问AI反馈统计页面
- 菜单顺序：欢迎页面、用户管理、地址管理（百度/腾讯）、AI点餐反馈统计

### 地址管理系统
- **百度地址管理**：门店信息维护，支持Excel导入导出，KFC API集成
- **腾讯地址管理**：腾讯地图服务同步，专门处理腾讯格式数据

### 用户管理系统
- 用户CRUD操作
- 支持管理员创建、编辑、删除用户
- 集成Supabase Authentication

### AI反馈统计系统
- 展示用户反馈数据
- 支持多条件搜索和Excel导出
- 生产/测试环境切换

## 开发规范

### 组件组织
- 页面级组件位于 `src/pages/`
- 复用组件放在对应页面的 `components/` 子目录
- 全局组件位于 `src/components/`

### API调用规范
- 使用统一的API服务层
- Supabase操作封装在 `supabase/` 目录
- 外部API调用封装在 `src/utils/` 目录

### 路由权限
- 所有路由配置在 [routes.ts](mdc:config/routes.ts)
- 权限控制在 [access.ts](mdc:src/access.ts)
- 基于 `ant_design_role` 字段进行权限判断

## 数据表结构

### Supabase表
- `address_baidu` - 百度地址数据表
- `address_tencent` - 腾讯地址数据表  
- `user_profiles` - 用户资料表
- `auth.users` - Supabase认证用户表

### 关键字段
- `ant_design_role` - 用户角色字段（admin/user）
- `user_id` - 用户ID，关联认证表
- `storeCodeId` - 门店ID
- `longitude/latitude` - 经纬度坐标

## 开发命令

### 常用命令
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm start

# 构建生产版本
pnpm build

# 代码检查
pnpm lint

# 代码格式化
pnpm prettier

# 运行测试
pnpm test
```

## 环境变量配置

### 必需的环境变量 (.env)
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 特殊API配置
- **KFC API**: 门店信息查询和电话获取
- **腾讯地图API**: 门店信息同步到腾讯地图服务
- **AI反馈API**: 点餐反馈数据查询（支持生产/测试环境切换）

## 项目迭代记录
详细的功能迭代记录请参考各页面的 rules 文件：
- [百度地址管理](mdc:.cursor/rules/pages/address-baidu.mdc) - 完整的功能演进历史
- [腾讯地址管理](mdc:.cursor/rules/pages/address-tencent.mdc) - 腾讯集成相关记录
- [AI反馈统计](mdc:.cursor/rules/pages/ai-feedback.mdc) - 反馈功能实现历史
- [用户管理](mdc:.cursor/rules/pages/user-management.mdc) - 用户管理功能记录

## 重要约定

### 代码组织
- 每个页面的子组件放在对应的 `components/` 目录下
- 复用的工具函数放在 `src/utils/` 目录
- API接口按功能模块分类组织
- 类型定义与对应的功能模块放在一起

### 数据流
- 前端 → API服务层 → Supabase数据服务 → 数据库
- 外部API调用通过工具函数封装，统一错误处理
- 权限控制在路由层面和页面组件层面双重保护

### 命名规范
- 文件名使用PascalCase（组件）或camelCase（工具函数）
- 数据表使用snake_case命名
- API接口遵循RESTful风格
- 组件props使用TypeScript严格类型检查
